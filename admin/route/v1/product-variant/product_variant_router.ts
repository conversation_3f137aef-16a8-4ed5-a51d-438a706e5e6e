/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminProductVariantController = () =>
  import('#adminControllers/product-variant/product_variant_controller')

export default function adminProductVariantRoutes() {
  const prefix = 'product-variant'
  router
    .group(() => {
      router
        .group(() => {
          router.get('select', [AdminProductVariantController, 'select'])
        })
        .prefix(prefix)

      router.resource(prefix, AdminProductVariantController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}

import { middleware } from "#start/kernel"
import router from "@adonisjs/core/services/router"

const AdminVendorController = () => import("#adminControllers/vendors/admin_vendor_controller");
const AdminVendorCommissionController = () => import("#adminControllers/vendors/admin_vendor_commissions_controller");

export default function adminVendorRoutes() {
  router
    .group(() => {
      router.get('vendors/settings', [AdminVendorController, 'settings']);

      router.get('vendors/commissions', [AdminVendorCommissionController, 'index']);
      router.get('vendors/commissions/:id', [AdminVendorCommissionController, 'show']);
      router.put('vendors/commissions/:id', [AdminVendorCommissionController, 'update']);
      router.delete('vendors/commissions/:id', [AdminVendorCommissionController, 'destroy']);
      router.get('vendors/:vendorId/commissions', [AdminVendorCommissionController, 'index']);

      router.resource('vendors', AdminVendorController);
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }));
}
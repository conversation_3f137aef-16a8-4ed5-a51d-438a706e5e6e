import Chat<PERSON>ele<PERSON><PERSON><PERSON> from '#jobs/chat_delete_job'
import Chat<PERSON><PERSON><PERSON><PERSON> from '#jobs/chat_event_job'
import Chat<PERSON><PERSON>ageJob from '#jobs/chat_message_job'
import ZnAdmin from '#models/zn_admin'
import ZnChatMessage from '#models/zn_chat_message'
import Zn<PERSON>hatRoom from '#models/zn_chat_room'
import { IvschatService } from '#services/aws/ivschat_service'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'

export default class AdminChatController {
    private ivsChatService: IvschatService

    constructor() {
        this.ivsChatService = new IvschatService()
    }

    /**
     * @createChatRoom
     * @tag Admin Chat
     * @summary Create Chat Room
     * @requestBody {"name":""}
     * @responseBody 200 - <ZnChatRoom> - Create Chat Room
     */
    async createChatRoom({ request, response }: HttpContext) {
        const data = request.body()

        const room = await this.ivsChatService.createRoom({ name: data.name })

        const dbRoom = await ZnChatRoom.create({
            arn: room.arn,
            name: room.name,
        })

        return response.ok(dbRoom)
    }

    /**
     * @getChatRooms
     * @tag Admin Chat
     * @summary Get Chat Rooms
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @responseBody 200 - <ZnChatRoom[]>.paginated() - Get Chat Rooms descriptively
     */
    async getChatRooms({ request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
        } = request.qs()

        try {
            // return rooms with latest message first, then latest newly created rooms
            const roomsFilter = await db.query()
                .from('zn_chat_rooms')
                .whereNull('zn_chat_rooms.deletedAt')
                .leftJoin('zn_chat_messages', 'zn_chat_rooms.id', 'zn_chat_messages.roomId')
                .whereNull('zn_chat_messages.deletedAt')
                .groupBy('zn_chat_rooms.id')
                .orderByRaw('COALESCE(MAX(zn_chat_messages.createdAt), zn_chat_rooms.createdAt) DESC')
                .select(
                    'zn_chat_rooms.*',
                    db.raw('MAX(zn_chat_messages.createdAt) AS latestMessageAt'),
                )
                .paginate(page, limit)

            const rooms = await ZnChatRoom.query()
                .whereIn('id', roomsFilter.map(room => room.id))

            const meta = roomsFilter.toJSON().meta
            const data = roomsFilter.toJSON().data

            const newData = rooms.map(room => ({
                ...room.serialize(),
                ...data.find(datum => datum.id == room.id)
            }))

            return response.ok({
                ...meta,
                data: newData
            })

        } catch (error) {
            return response.internalServerError(error)
        }
    }

    /**
     * @createChatToken
     * @tag Admin Chat
     * @summary Chat Token provider
     * @paramPath id - ID of Chat Room - @required
     * @responseBody 200 - {"token":"","tokenExpirationTime":"2025-02-18T22:44:57.000Z","tokenExpirationTime","2025-02-18T22:44:57.000Z"} - ChatToken provider for IVS ChatRoom
     */
    async createChatToken({ auth, params, response }: HttpContext) {
        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)

        if (!room) { return response.notFound({ message: 'Chat Room not found' }) }

        const admin = auth.getUserOrFail() as ZnAdmin
        await admin?.load('avatar')

        const token = await this.ivsChatService.createChatToken({
            roomArn: room.arn,
            userId: admin.id,
            capabilities: ["SEND_MESSAGE", "DISCONNECT_USER", "DELETE_MESSAGE"],
            attributes: {
                type: "admin",
                username: admin.username,
                name: admin.name,
                avatarUrl: admin.avatar?.url
            }
        })        

        return response.ok(token)
    }

    /**
     * @sendChatEvent
     * @tag Admin Chat
     * @summary Send chat event
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"event":"","attributes":{}}
     * @responseBody 200 - ChatEventJob Sent. - Send chat event descriptively
     */
    async sendChatEvent({ params, request, response }: HttpContext) {
        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)

        if (!room) { return }

        const data = request.body() as any

        const ivschatService = new IvschatService()
        await ivschatService.sendEvent(room.arn, data.eventName, data.attributes)

        await queue.dispatch(
            ChatEventJob,
            { roomId, data, }
        )

        return response.ok("StreamChatEventJob Sent.")
    }

    /**
     * @getMessages
     * @tag Admin Chat
     * @summary Get Chat Room Messages
     * @paramPath id - ID of Chat Room - @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @paramQuery filterIvsIds - IVS IDs of Chat Messages to be filtered (e.g. "wNbmMLv3EuWl") - @type(array)
     * @responseBody 200 - <ZnChatMessage>.paginated() - Get Chat Room Messages descriptively
     */
    async getMessages({ params, request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            filterIvsIds = []
        } = request.qs()

        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)
        if (!room) { return response.notFound("Chat Room Not Found") }

        const messages = await ZnChatMessage.query()
            .where({ roomId })
            .preload('user', (userQuery) => {
                userQuery.preload('avatarMedia')
            })
            .preload('admin', (adminQuery) => {
                adminQuery.preload('avatar')
            })
            .whereNull('parentMessageId')
            .whereNotIn('ivsChatMessageId', filterIvsIds)
            .withCount('children')
            .orderBy('createdAt', 'desc')
            .paginate(page, limit)

        const totalChats = await ZnChatMessage.query()
            .where({ roomId })
            .count('* AS count')
            .first()

        return response.ok({
            ...messages.serialize(),
            totalChats: totalChats?.$extras.count
        })
    }

    /**
     * @getChildren
     * @tag Admin Chat
     * @summary Get child messages of chat
     * @paramPath id - IVS ID or UUID of Chat Message - @required
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @responseBody 200 - <ZnChatMessage>.paginated() - Send chat event descriptively
     */
    async getChildren({ params, request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            filterIvsIds = []
        } = request.qs()

        const messageId = params.id

        const message = await ZnChatMessage.query()
            .where({ id: messageId })
            .orWhere({ ivsChatMessageId: messageId })
            .first()

        if (!message) { return response.notFound("Chat Message Not Found") }

        const children = await ZnChatMessage.query()
            .preload('parentMessage')
            .preload('user', (userQuery) => {
                userQuery.preload('avatarMedia')
            })
            .preload('admin', (adminQuery) => {
                adminQuery.preload('avatar')
            })
            .where({ parentMessageId: message.id })
            .whereNotIn('ivsChatMessageId', filterIvsIds)
            .orderBy('createdAt', 'asc')
            .paginate(page, limit)

        return response.ok(children)
    }

    /**
     * @createChatMessage
     * @tag Admin Chat
     * @summary Create chat message
     * @paramPath id - ID of Chat Room - @required
     * @requestBody {"id":"wNbmMLv3EuWl","content":"A message","attributes":{"parentMessageIvsId":"LWuE3vLMmbNw"}}
     * @responseBody 200 - StreamCommentJob Sent. - Send job to save chat message as post comment
     */
    async createChatMessage({ auth, params, request, response }: HttpContext) {        
        const roomId = params.id

        const room = await ZnChatRoom.find(roomId)
        if (!room) { return response.notFound("Chat Room Not Found") }

        const data = request.body() as any

        if (data.attributes?.parentMessageIvsId) {
            const reply = await ZnChatMessage.findBy({ ivsChatMessageId: data.attributes?.parentMessageIvsId })
            if (!reply) { return response.notFound("Chat Reply Message Not Found") }
        }

        const currentAdmin = auth.getUserOrFail() as ZnAdmin

        await queue.dispatch(
            ChatMessageJob,
            {
                roomId,
                data: {
                    ...data,
                    sender: {
                        adminId: currentAdmin.id,
                    }
                },
            },
        )

        return response.ok("StreamCommentJob Sent.")
    }

    /**
     * @deleteChatMessage
     * @tag Admin Chat
     * @summary Delete chat message
     * @paramPath id - IVS ID or UUID of Chat Message - @required @example(wNbmMLv3EuWl)
     * @responseBody 200 - StreamChatDeleteJob Sent. - Delete Chat Message
     */
    async deleteChatMessage({ params, response }: HttpContext) {
        const ivsChatMessageId = params.id

        await queue.dispatch(
            ChatDeleteJob,
            { ivsChatMessageId },
        )

        return response.ok("StreamChatDeleteJob Sent.")
    }
}

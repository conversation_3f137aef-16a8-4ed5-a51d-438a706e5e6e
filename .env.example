TZ=
PORT=
HOST=
LOG_LEVEL=
APP_KEY=
NODE_ENV=
ZURNO_ADMIN_USER=
ZURNO_ADMIN_PASS=

DB_CONNECTION=
DB_HOST=
DB_REPLICA_HOST=
DB_PORT=
DB_DATABASE=
DB_USER=
DB_PASSWORD=




# DB_HOST=ls-e26cd1e87f97907ef42a0117ab5390b37d8216b5.ctew66io48xk.us-east-1.rds.amazonaws.com
# DB_REPLICA_HOST=ls-e26cd1e87f97907ef42a0117ab5390b37d8216b5.ctew66io48xk.us-east-1.rds.amazonaws.com
# DB_PORT=3306
# DB_ROOT_PASSWORD=G.YPCaB[Y0_vk7G1x`WdT:!Qyt{P3dpF
# DB_DATABASE=db_main
# DB_USER=dbmasteruser
# DB_PASSWORD=G.YPCaB[Y0_vk7G1x`WdT:!Qyt{P3dpF

ZURNO_DB_HOST=
ZURNO_DB_PORT=
ZURNO_DB_ROOT_PASSWORD=
ZURNO_DB_DATABASE=
ZURNO_DB_USER=
ZURNO_DB_PASSWORD=

ZURNO_MAIN_DB_HOST=
ZURNO_MAIN_DB_ROOT_PASSWORD=
ZURNO_MAIN_DB_PORT=
ZURNO_MAIN_DB_DATABASE=
ZURNO_MAIN_DB_USER=
ZURNO_MAIN_DB_PASSWORD=

FIREBASE_CLIENT_EMAIL=
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_DATABASE_URL=

MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=
SMTP_HOST=
SMTP_PORT=
SMTP_USERNAME=
SMTP_PASSWORD=

SHOPIFY_STORE_DOMAIN=
SHOPIFY_ACCESS_TOKEN=
SHOPIFY_REVIEW_DOMAIN=
SHOPIFY_REVIEW_SID=
SHOPIFY_REVIEW_API_KEY=
SHOPIFY_VERSION=
SHOPIFY_WEBHOOK_CLIEN_KEY=
SHOPIFY_STOREFRONT_ACCESS_TOKEN=
SHOPIFY_STOREFRONT_VERSION=

PROMO_API_URL=
PROMO_API_PUBLIC=
PROMO_API_PRIVATE=

AWS_S3_BUCKET_NAME=
AWS_S3_REGION=
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=

QUEUE_REDIS_HOST=
QUEUE_REDIS_PORT=
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_TLS=

# TRACKING_DB_HOST=127.0.0.1
# TRACKING_DB_PORT=3306
# TRACKING_DB_DATABASE=db_tracking
# TRACKING_DB_USER=root
# TRACKING_DB_PASSWORD=Zurno@123456

TRACKING_DB_HOST=
TRACKING_DB_PORT=
TRACKING_DB_DATABASE=
TRACKING_DB_USER=
TRACKING_DB_PASSWORD=

ADS_WEBSITE_DOMAIN=
# SUPPORT_EMAIL=<EMAIL>
SUPPORT_EMAIL=
# SUPPORT_EMAIL=<EMAIL>
CHANGE_PRICE_EMAILS=
SUPPORT_PHONE_NUMBER=
INVENTORY_EMAIL=
ZURNO_ADMIN_USER=
# ZURNO_ADMIN_PASS=swordfish
ZURNO_ADMIN_PASS=
DRIVE_DISK=
OPENAI_API_KEY=
GOOGLE_PLACES_API_KEY=

INSTAGRAM_URL=

BASE_URL=
FULFIL_WEBHOOK_CLIEN_KEY=
INVENTORY_EMAIL=
DRIVE_DISK=

AWS_SNS_ACCESS_KEY_ID=
AWS_SNS_SECRET_ACCESS_KEY=
AWS_SNS_REGION=

CRON_JOB_ENABLE=

STAMPED_API_URL=
STAMPED_API_STORE_HASH=
STAMPED_API_KEY=
STAMPED_API_SECRET=
STAMPED_REVIEW_PHOTO_URL=
SUPPORT_PHONE_NUMBER=

AWS_CONNECT_INSTANCE_ID=
AWS_CONNECT_CONTACT_FLOW_ID=
AWS_CONNECT_PHONE_NUMBER=

FAST_BUNDLE_API_URL=
SHOP_DOMAIN=

AWS_SNS_ACCESS_KEY_ID=
AWS_SNS_SECRET_ACCESS_KEY=
AWS_SNS_REGION=

CRON_JOB_ENABLE=
GOOGLE_APPLICATION_CREDENTIALS=
FUFIL_DATAWAREHOUSE_APPLICATION_ID=

AWS_IVS_ACCESS_KEY_ID=
AWS_IVS_SECRET_ACCESS_KEY=
AWS_IVS_RECORDING_CONFIG=
AWS_IVS_DEFAULT_CHANNEL=

SOCKET_ENABLED=
SOCKET_PORT=

PRODUCT_BUNDLE_TAG_NAME=

# Shop section tags
SHOP_FLASH_SALE_TAG=
SHOP_NEW_ARRIVALS_TAG=
SHOP_BEST_SELLERS_TAG=

# PINECONEDB FOR PRODUCT EMBEDDING
PINECONE_API_KEY=
PROD_EMBEDDING_INDEX_NAME=
COLLECTION_EMBEDDING_INDEX_NAME=
POST_EMBEDDING_INDEX_NAME=


# SETUP ASSISTANT
CUSTOMER_SERVICE=
ORDER_ASSISTANT=
SHOPPING_ASSISTANT=
POST_ASSISTANT=
RECEPTIONIST_SERVICE=
CHATBOT_TIMING=

#OPENAI VECTORSOTRE
PRODUCTS_VECTOR_STORE=

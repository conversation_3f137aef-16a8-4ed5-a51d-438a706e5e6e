# Dependencies and AdonisJS build
node_modules
build
tmp
storage

# Secrets
.env
.env.local
.env.production.local
.env.development.local
.env.staging

# Frontend assets compiled code
public/assets

# Build tools specific
npm-debug.log
yarn-error.log

# Editors specific
.fleet
.idea
.vscode

# Platform specific
.DS_Store
.history
/.yarn/
vite.config.ts.*

/app/services/chatbot/documents/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
// Comprehensive test script to verify all filters work consistently with all sort methods
const axios = require('axios')

const BASE_URL = 'http://localhost:3333' // Adjust if needed
const TEST_CATEGORY_ID = '719cb104-33bb-4a17-b98f-441a6c675a1a'

// Test configurations
const FILTERS = {
  category: { categoryIds: TEST_CATEGORY_ID },
  price: { priceFrom: '10', priceTo: '1000' },
  location: { latitude: '25.7617', longitude: '-80.1918', miles: '50' }, // Miami coordinates
  search: { search: 'service' },
}

const SORT_METHODS = ['newest', 'popular', 'distance']

async function makeRequest(params) {
  try {
    const response = await axios.get(`${BASE_URL}/v1/post/`, {
      params: {
        allCountry: true,
        page: 1,
        limit: 30,
        ...params,
      },
    })
    return {
      success: true,
      total: response.data.meta.total,
      data: response.data.data,
      firstPost: response.data.data[0]?.title || 'No posts',
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
    }
  }
}

async function testFilterWithAllSorts(filterName, filterParams) {
  console.log(`\n🧪 Testing ${filterName.toUpperCase()} filter with all sort methods:`)
  console.log('='.repeat(60))

  const results = {}

  for (const sortMethod of SORT_METHODS) {
    console.log(`\n📋 ${filterName} + sortBy=${sortMethod}`)

    const params = {
      ...filterParams,
      sortBy: sortMethod,
    }

    const result = await makeRequest(params)

    if (result.success) {
      results[sortMethod] = result.total
      console.log(`✅ Results: ${result.total} posts found`)
      console.log(`📄 First post: ${result.firstPost}`)

      if (sortMethod === 'popular' && result.data[0]?.popularityScore !== undefined) {
        console.log(`⭐ Popularity score: ${result.data[0].popularityScore}`)
      }
      if (sortMethod === 'distance' && result.data[0]?.distance !== undefined) {
        console.log(`📍 Distance: ${result.data[0].distance?.toFixed(2)} miles`)
      }
    } else {
      console.log(`❌ ERROR: ${result.error} (Status: ${result.status})`)
      results[sortMethod] = 'ERROR'
    }
  }

  // Compare results
  console.log(`\n🔍 ${filterName.toUpperCase()} COMPARISON:`)
  const totals = Object.values(results).filter((v) => v !== 'ERROR')
  const uniqueTotals = [...new Set(totals)]

  if (uniqueTotals.length === 1 && totals.length === SORT_METHODS.length) {
    console.log(`✅ SUCCESS: All sort methods return the same total (${uniqueTotals[0]} posts)`)
    console.log(`✅ ${filterName} filtering is working correctly with all sort methods!`)
  } else {
    console.log(`❌ FAILED: Inconsistent results across sort methods:`)
    SORT_METHODS.forEach((sort) => {
      console.log(`   ${sort}: ${results[sort]}`)
    })
  }

  return results
}

async function testAllCombinations() {
  console.log('🚀 COMPREHENSIVE FILTER + SORT TESTING')
  console.log('Testing that all filters return consistent totals across all sort methods\n')

  const allResults = {}

  // Test each filter type
  for (const [filterName, filterParams] of Object.entries(FILTERS)) {
    allResults[filterName] = await testFilterWithAllSorts(filterName, filterParams)
  }

  // Test combination filters
  console.log(`\n🧪 Testing COMBINATION filters:`)
  console.log('='.repeat(60))

  // Category + Price
  allResults['category+price'] = await testFilterWithAllSorts('category+price', {
    ...FILTERS.category,
    ...FILTERS.price,
  })

  // Category + Location
  allResults['category+location'] = await testFilterWithAllSorts('category+location', {
    ...FILTERS.category,
    ...FILTERS.location,
  })

  // Final summary
  console.log(`\n📊 FINAL SUMMARY:`)
  console.log('='.repeat(60))

  let allPassed = true
  for (const [filterName, results] of Object.entries(allResults)) {
    const totals = Object.values(results).filter((v) => v !== 'ERROR')
    const uniqueTotals = [...new Set(totals)]
    const passed = uniqueTotals.length === 1 && totals.length === SORT_METHODS.length

    console.log(`${passed ? '✅' : '❌'} ${filterName}: ${passed ? 'PASSED' : 'FAILED'}`)
    if (!passed) {
      allPassed = false
      console.log(`   Results: ${JSON.stringify(results)}`)
    }
  }

  console.log(
    `\n${allPassed ? '🎉' : '💥'} Overall: ${allPassed ? 'ALL TESTS PASSED!' : 'SOME TESTS FAILED!'}`
  )
}

// Run the comprehensive test
testAllCombinations().catch(console.error)

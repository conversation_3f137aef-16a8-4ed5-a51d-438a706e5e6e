// Test script to verify category filtering with sortBy works correctly
const axios = require('axios');

const BASE_URL = 'http://localhost:3333'; // Adjust if needed
const TEST_CATEGORY_ID = '719cb104-33bb-4a17-b98f-441a6c675a1a';

async function testCategoryFilter() {
  console.log('🧪 Testing Category Filter with sortBy...\n');

  try {
    // Test 1: Category + sortBy=newest
    console.log('📋 Test 1: Category + sortBy=newest');
    const newestResponse = await axios.get(`${BASE_URL}/v1/post/`, {
      params: {
        allCountry: true,
        page: 1,
        limit: 30,
        categoryIds: TEST_CATEGORY_ID,
        sortBy: 'newest'
      }
    });
    
    const newestTotal = newestResponse.data.meta.total;
    console.log(`✅ Results: ${newestTotal} posts found`);
    console.log(`📄 First post: ${newestResponse.data.data[0]?.title || 'No posts'}\n`);

    // Test 2: Category + sortBy=popular
    console.log('📋 Test 2: Category + sortBy=popular');
    const popularResponse = await axios.get(`${BASE_URL}/v1/post/`, {
      params: {
        allCountry: true,
        page: 1,
        limit: 30,
        categoryIds: TEST_CATEGORY_ID,
        sortBy: 'popular'
      }
    });
    
    const popularTotal = popularResponse.data.meta.total;
    console.log(`✅ Results: ${popularTotal} posts found`);
    console.log(`📄 First post: ${popularResponse.data.data[0]?.title || 'No posts'}`);
    console.log(`⭐ Popularity score: ${popularResponse.data.data[0]?.popularityScore || 'N/A'}\n`);

    // Test 3: Compare results
    console.log('🔍 Comparison:');
    if (newestTotal === popularTotal) {
      console.log(`✅ SUCCESS: Both queries return the same total (${newestTotal} posts)`);
      console.log('✅ Category filtering is working correctly with both sort methods!');
    } else {
      console.log(`❌ FAILED: Different totals - newest: ${newestTotal}, popular: ${popularTotal}`);
      console.log('❌ Category filtering is not working correctly with popular sort');
    }

    // Test 4: No category filter (baseline)
    console.log('\n📋 Test 4: No category filter (baseline)');
    const allPostsResponse = await axios.get(`${BASE_URL}/v1/post/`, {
      params: {
        allCountry: true,
        page: 1,
        limit: 30,
        sortBy: 'popular'
      }
    });
    
    const allPostsTotal = allPostsResponse.data.meta.total;
    console.log(`📊 Total posts without filter: ${allPostsTotal}`);
    
    if (popularTotal < allPostsTotal) {
      console.log('✅ Category filter is reducing results as expected');
    } else {
      console.log('⚠️  Category filter might not be working - same total as unfiltered');
    }

  } catch (error) {
    console.error('❌ Error testing:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testCategoryFilter();

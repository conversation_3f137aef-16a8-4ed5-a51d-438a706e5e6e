{"models": {"main": {"provider": "google", "modelId": "gemini-2.0-flash-lite", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.0-flash-lite", "maxTokens": 1048000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Task Master", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "userId": "**********", "defaultTag": "master"}}
# AI Receptionist Appointment Integration - Product Requirements Document

## 📋 Executive Summary

### Business Objective
Integrate the existing AI Receptionist system with the appointment booking and store service management systems to enable seamless voice-based appointment booking for salon and service businesses.

### Current State
- ✅ **AI Receptionist Framework**: Twilio voice integration, OpenAI processing, conversation management
- ✅ **Appointment System**: Complete CRUD operations, status management, service/package relationships  
- ✅ **Store Service System**: Services, categories, packages, taxes management
- ✅ **Customer Management**: User profiles, store relationships
- ❌ **Integration Gap**: AI conversations don't connect to actual appointment booking

### Target State
A fully integrated AI receptionist that can:
- Identify callers and stores automatically
- Understand booking requests through natural language
- Check availability and suggest alternatives
- <PERSON><PERSON> confirmed appointments with proper customer and service information
- Send confirmations and integrate with calendars

---

## 🎯 Business Requirements

### Primary Goals
1. **Seamless Voice Booking**: Enable customers to book appointments through natural conversation
2. **Operational Efficiency**: Reduce staff workload for appointment scheduling
3. **Customer Experience**: Provide 24/7 booking availability with human-like interaction
4. **Business Growth**: Increase booking conversion rates and customer satisfaction

### Success Metrics
- **Booking Completion Rate**: Target 80% of attempted voice bookings
- **Call Resolution Time**: Average < 3 minutes per booking
- **Customer Satisfaction**: Target 4.5/5 rating for AI booking experience
- **Staff Efficiency**: 50% reduction in manual appointment scheduling time
- **Error Rate**: < 5% booking errors requiring human intervention

---

## 🔄 User Flow Analysis

### Current AI Receptionist Flow (From Flowcharts)

```mermaid
graph TD
    A[Customer Calls] --> B[AI Receptionist Answers]
    B --> C[Collect Information]
    C --> D{Check Availability}
    D -->|Available| E[Confirm Information]
    D -->|Not Available| F[Offer Alternatives]
    F --> E
    E --> G[Booking Successful]
    G --> H[Email Confirmation]
    G --> I[Create Calendar Entry]
```

### Information Collection Requirements
**Essential Data:**
- Customer Name
- Customer Phone Number  
- Desired Service(s)
- Preferred Date & Time
- Special Notes (Optional)

**AI Conversation Prompts:**
1. **Greeting**: "Hello! How can I assist you today?"
2. **Service Identification**: "Which service would you like to book?"
3. **Scheduling**: "What date and time work best for you?"
4. **Customer Information**: "May I have your name and phone number?"
5. **Confirmation**: "Let me confirm your [Service] appointment on [Date] at [Time]"

---

## 🏗️ Technical Architecture

### Current System Components

#### Existing Models
- `ZnCall`: Call session management with store/customer identification
- `ZnCallMessage`: Conversation history and AI responses
- `ZnAppointment`: Appointment records with status workflow
- `ZnStore`: Store information with phone mapping and booking settings
- `ZnUser`: Customer profiles with store relationships
- `ZnStoreService`: Available services with pricing and duration
- `ZnStorePackage`: Service bundles and packages
- `ZnStoreTax`: Tax calculations

#### Existing Services
- `ZurnoAIReceptionistService`: Basic Twilio/OpenAI integration
- `AppointmentService`: Complete appointment CRUD operations
- `ZurnoAssistantService`: AI agent framework (RECEPTIONIST_SERVICE role pending)

#### Current Integration Gaps
1. **Store Context Loading**: AI doesn't know which store is being called
2. **Customer Identification**: No automatic customer lookup by phone
3. **Service Discovery**: AI can't access store's available services
4. **Booking Integration**: No connection between AI conversation and appointment creation
5. **Availability Checking**: No real-time slot validation

---

## 🔧 Implementation Requirements

### Phase 1: Core Integration (MVP)
**Duration**: 2-3 weeks  
**Priority**: Critical

#### 1.1 Store Context Integration
**Requirement**: Enable AI to identify which store is being called and load store-specific context.

**Technical Implementation**:
```typescript
// Enhance ZurnoAIReceptionistService.welcome()
async welcome({ request, response }: HttpContext) {
  const toNumber = request.input('To');
  
  // NEW: Store identification by phone number
  const store = await ZnStore.query()
    .where('phoneNumber', toNumber)
    .where('isManageBookingEnabled', true)
    .first();
    
  if (!store) {
    return this.handleUnsupportedStore(response);
  }
  
  // Load store context for AI
  const storeContext = await this.loadStoreContext(store.id);
  
  // Store context in call record
  await ZnCall.create({
    fromNumber,
    toNumber,
    clientCallId: callSid,
    storeId: store.id, // NEW: Store identification
    threadId
  });
}

private async loadStoreContext(storeId: string) {
  const store = await ZnStore.query()
    .where('id', storeId)
    .preload('user')
    .first();
    
  const services = await ZnStoreService.query()
    .where('storeId', storeId)
    .preload('categories');
    
  const packages = await ZnStorePackage.query()
    .where('storeId', storeId)
    .preload('services');
    
  return {
    store,
    services,
    packages,
    workingHours: store.workingHour
  };
}
```

#### 1.2 Customer Management Integration
**Requirement**: Automatically identify returning customers and create new customer records.

**Technical Implementation**:
```typescript
private async findOrCreateCustomer(phoneNumber: string, storeId: string, name?: string) {
  // Check if customer exists in system
  let customer = await ZnUser.query()
    .where('phone', phoneNumber)
    .first();
    
  if (!customer && name) {
    // Create new customer
    const [firstName, ...lastNameParts] = name.split(' ');
    customer = await ZnUser.create({
      phone: phoneNumber,
      firstName,
      lastName: lastNameParts.join(' '),
      email: `${phoneNumber}@phone.zurno.com`, // Placeholder email
      active: true
    });
  }
  
  if (customer) {
    // Link customer to store if not already linked
    await customer.related('customerStores').syncWithoutDetaching([storeId]);
  }
  
  return customer;
}
```

#### 1.3 AI Conversation Processing
**Requirement**: Implement actual AI conversation processing with booking intent recognition.

**Technical Implementation**:
```typescript
// Replace placeholder in handleReception()
async handleReception(room: ZnCall, message: ZnCallMessage, userSaid: string) {
  const assistant = await ZnAIAssistant.findBy({role: EAIAssistantRole.RECEPTIONIST_SERVICE});
  
  if (!assistant) {
    throw new Error('Receptionist AI Assistant not configured');
  }
  
  // Load store and customer context
  const storeContext = await this.loadStoreContext(room.storeId);
  const customerContext = await this.loadCustomerContext(room.fromNumber, room.storeId);
  
  // Create enhanced system prompt with context
  const systemPrompt = this.buildReceptionistPrompt(storeContext, customerContext);
  
  // Process conversation with OpenAI
  const aiResponse = await this.processAIConversation({
    threadId: room.threadId,
    userMessage: userSaid,
    systemPrompt,
    storeContext,
    customerContext
  });
  
  // Store AI response
  message.reply = aiResponse.message;
  await message.save();
  
  // Check for booking intent and process if detected
  if (aiResponse.intent === 'BOOK_APPOINTMENT') {
    await this.processBookingIntent(aiResponse.data, room, storeContext);
  }
}

private buildReceptionistPrompt(storeContext: any, customerContext: any) {
  return `You are a professional receptionist for ${storeContext.store.name}.

STORE INFORMATION:
- Services: ${storeContext.services.map(s => `${s.name} ($${s.price}, ${s.duration}min)`).join(', ')}
- Working Hours: ${JSON.stringify(storeContext.workingHours)}
- Phone: ${storeContext.store.phoneNumber}

CUSTOMER CONTEXT:
${customerContext ? `- Returning customer: ${customerContext.firstName} ${customerContext.lastName}` : '- New customer'}

CAPABILITIES:
- Schedule appointments for available services
- Check availability and suggest alternative times
- Provide service information and pricing
- Handle appointment modifications

INSTRUCTIONS:
- Be friendly and professional
- Always confirm appointment details before booking
- If requested time is unavailable, suggest 2-3 alternatives
- Collect: customer name, phone, service, date/time, any special requests
- End conversations with booking confirmation or next steps

Current date/time: ${new Date().toISOString()}`;
}
```

#### 1.4 Basic Appointment Creation
**Requirement**: Create appointments from successful AI conversations.

**Technical Implementation**:
```typescript
private async processBookingIntent(bookingData: any, room: ZnCall, storeContext: any) {
  try {
    // Find or create customer
    const customer = await this.findOrCreateCustomer(
      room.fromNumber, 
      room.storeId, 
      bookingData.customerName
    );
    
    if (!customer) {
      throw new Error('Unable to identify customer');
    }
    
    // Find requested service
    const service = await ZnStoreService.query()
      .where('storeId', room.storeId)
      .where('name', 'ilike', `%${bookingData.serviceName}%`)
      .first();
      
    if (!service) {
      throw new Error(`Service "${bookingData.serviceName}" not found`);
    }
    
    // Check availability (basic implementation)
    const requestedTime = DateTime.fromISO(bookingData.dateTime);
    const endTime = requestedTime.plus({ minutes: service.duration });
    
    const isAvailable = await this.checkBasicAvailability(
      room.storeId, 
      requestedTime, 
      endTime
    );
    
    if (!isAvailable) {
      throw new Error('Requested time slot not available');
    }
    
    // Create appointment using existing service
    const appointmentService = new AppointmentService();
    const appointment = await appointmentService.create({
      storeId: room.storeId,
      customerId: customer.id,
      startTime: requestedTime.toJSDate(),
      endTime: endTime.toJSDate(),
      notes: bookingData.notes || `Booked via AI Receptionist - Call ID: ${room.clientCallId}`,
      services: [service.id]
    });
    
    // Update call record with appointment
    room.appointmentId = appointment.id;
    await room.save();
    
    return appointment;
    
  } catch (error) {
    console.error('Booking creation failed:', error);
    throw error;
  }
}

private async checkBasicAvailability(storeId: string, startTime: DateTime, endTime: DateTime): Promise<boolean> {
  const conflictingAppointments = await ZnAppointment.query()
    .where('storeId', storeId)
    .where('status', '!=', EAppointmentStatus.CANCELLED)
    .where((query) => {
      query
        .whereBetween('startTime', [startTime.toJSDate(), endTime.toJSDate()])
        .orWhereBetween('endTime', [startTime.toJSDate(), endTime.toJSDate()])
        .orWhere((subQuery) => {
          subQuery
            .where('startTime', '<=', startTime.toJSDate())
            .where('endTime', '>=', endTime.toJSDate());
        });
    });
    
  return conflictingAppointments.length === 0;
}
```

#### 1.5 Database Schema Updates
**Requirement**: Add appointment tracking to call records.

**Migration**:
```typescript
// database/migrations/create_add_appointment_id_to_calls.ts
export default class extends BaseSchema {
  protected tableName = 'zn_calls'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('appointmentId').nullable()
      table.foreign('appointmentId').references('id').inTable('zn_appointments')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropForeign(['appointmentId'])
      table.dropColumn('appointmentId')
    })
  }
}
```

### Phase 2: Advanced Features (Enhancement)
**Duration**: 3-4 weeks  
**Priority**: High

#### 2.1 Natural Language Processing Enhancement
**Features**:
- Parse complex date/time expressions ("next Monday morning", "this Friday at 2")
- Understand service variations ("mani-pedi", "cut and color", "facial treatment")
- Handle multi-service bookings
- Extract customer preferences and special requests

#### 2.2 Intelligent Availability Management
**Features**:
- Smart time slot suggestions based on service duration
- Buffer time management between appointments
- Staff availability integration (future)
- Seasonal/peak time handling

#### 2.3 Enhanced Customer Experience
**Features**:
- Customer history integration ("your usual service")
- Preference learning and suggestions
- Loyalty program integration
- Appointment reminders and follow-ups

#### 2.4 Advanced Booking Scenarios
**Features**:
- Package booking support
- Group appointments
- Recurring appointment setup
- Modification and cancellation support

### Phase 3: Enterprise Features (Future)
**Duration**: 4-6 weeks  
**Priority**: Medium

#### 3.1 Multi-Location Support
- Chain/franchise booking coordination
- Location-specific service offerings
- Cross-location customer management

#### 3.2 Advanced Analytics
- Booking conversion analytics
- Customer behavior insights
- AI performance optimization
- Revenue impact measurement

#### 3.3 Integration Ecosystem
- Calendar system integration (Google, Outlook)
- Payment processing integration
- Marketing automation integration
- CRM system synchronization

---

## 🔍 Quality Assurance Requirements

### Testing Strategy

#### 3.1 Unit Testing
```typescript
// Example test structure
describe('AI Receptionist Integration', () => {
  test('should identify store by phone number', async () => {
    // Test store identification logic
  });
  
  test('should create customer from phone conversation', async () => {
    // Test customer creation flow
  });
  
  test('should create appointment from AI conversation', async () => {
    // Test appointment booking integration
  });
  
  test('should handle unavailable time slots', async () => {
    // Test availability checking
  });
});
```

#### 3.2 Integration Testing
- End-to-end voice booking flows
- Error handling scenarios
- Performance under load
- Multi-concurrent call handling

#### 3.3 User Acceptance Testing
- Real customer conversation scenarios
- Edge case handling (unclear speech, complex requests)
- Accessibility compliance
- Multi-language support (future)

### Performance Requirements
- **Response Time**: < 3 seconds for AI processing
- **Availability**: 99.9% uptime
- **Concurrent Calls**: Support 10+ simultaneous calls per store
- **Scalability**: Support 100+ stores without performance degradation

---

## 🚨 Risk Management

### Technical Risks

#### 4.1 AI Processing Reliability
**Risk**: OpenAI API failures or slow responses
**Mitigation**: 
- Implement fallback to human transfer
- Add retry mechanisms with exponential backoff
- Cache common responses for faster processing

#### 4.2 Appointment Conflicts
**Risk**: Double-booking due to concurrent calls
**Mitigation**:
- Database-level constraints
- Real-time availability checking
- Immediate booking confirmation

#### 4.3 Voice Recognition Accuracy
**Risk**: Misunderstood customer requests
**Mitigation**:
- Confirmation loops for critical information
- Easy transfer to human agents
- Continuous AI model training

### Business Risks

#### 4.4 Customer Adoption
**Risk**: Customers prefer human interaction
**Mitigation**:
- Seamless human handoff option
- Clear communication about AI capabilities
- Gradual rollout with feedback collection

#### 4.5 Operational Impact
**Risk**: AI booking errors affect business operations
**Mitigation**:
- Comprehensive error logging and monitoring
- Manual override capabilities
- Staff training on AI system management

---

## 📊 Success Measurement

### Key Performance Indicators

#### Technical KPIs
- **Booking Success Rate**: % of calls that result in confirmed appointments
- **AI Processing Time**: Average time from speech to AI response
- **Error Rate**: % of bookings requiring manual correction
- **System Uptime**: % availability of AI booking system

#### Business KPIs
- **Customer Satisfaction**: Rating of AI booking experience
- **Operational Efficiency**: Reduction in manual booking time
- **Revenue Impact**: Additional bookings generated by 24/7 availability
- **Staff Productivity**: Time savings in appointment management

### Monitoring and Analytics

#### Real-time Dashboards
- Call volume and booking conversion rates
- AI processing performance metrics
- Error rates and failure categories
- Customer satisfaction scores

#### Regular Reporting
- Weekly performance summaries
- Monthly business impact analysis
- Quarterly optimization recommendations
- Annual ROI assessment

---

## 🛠️ Implementation Timeline

### Phase 1: Core Integration (Weeks 1-3)
- **Week 1**: Store context and customer management integration
- **Week 2**: AI conversation processing and intent recognition
- **Week 3**: Basic appointment creation and testing

### Phase 2: Enhanced Features (Weeks 4-7)
- **Week 4**: Natural language processing improvements
- **Week 5**: Advanced availability management
- **Week 6**: Customer experience enhancements
- **Week 7**: Multi-service booking support

### Phase 3: Production Deployment (Weeks 8-10)
- **Week 8**: Comprehensive testing and bug fixes
- **Week 9**: Pilot deployment with select stores
- **Week 10**: Full production rollout and monitoring

---

## 💼 Resource Requirements

### Development Team
- **1 Senior Backend Developer**: Lead integration implementation
- **1 AI/ML Engineer**: OpenAI integration and conversation optimization
- **1 QA Engineer**: Testing and quality assurance
- **1 DevOps Engineer**: Deployment and monitoring setup

### Infrastructure
- **OpenAI API Credits**: Estimated $500-1000/month for moderate usage
- **Twilio Voice Services**: Based on call volume
- **Redis Instances**: For session management scaling
- **Additional Database Storage**: For conversation logs and analytics

### External Dependencies
- OpenAI API access and credits
- Twilio account with voice capabilities
- Redis cluster for session storage
- Monitoring and alerting services

---

## 🎯 Conclusion

The AI Receptionist Appointment Integration represents a strategic enhancement to the Zurno platform that will:

1. **Transform Customer Experience**: Enable natural, 24/7 appointment booking
2. **Improve Operational Efficiency**: Reduce manual scheduling workload
3. **Drive Business Growth**: Increase booking conversion and customer satisfaction
4. **Establish Technology Leadership**: Position Zurno as an AI-first service platform

The existing technical foundation provides an excellent starting point, with the core infrastructure already in place. The integration focuses on connecting proven components rather than building from scratch, reducing implementation risk and accelerating time to market.

**Recommendation**: Proceed with Phase 1 implementation immediately, targeting a 3-week MVP delivery with core booking functionality. This will provide immediate value while establishing the foundation for advanced features in subsequent phases.

---

**Document Version**: 1.0  
**Created**: December 2024  
**Author**: Zurno Development Team  
**Status**: Ready for Implementation
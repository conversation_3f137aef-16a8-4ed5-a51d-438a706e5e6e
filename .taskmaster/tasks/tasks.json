{"master": {"tasks": [{"id": 1, "title": "Project Setup and Environment Configuration", "description": "Set up the project repository and initial development environment for the AI Receptionist integration.", "details": "Create a new repository (e.g., in GitHub, GitLab). Initialize the project with necessary dependencies (Node.js, TypeScript, relevant SDKs for Twilio, OpenAI, and database access). Configure the development environment, including linting, formatting, and testing tools.", "testStrategy": "Verify the environment setup by running basic tests and ensuring all dependencies are correctly installed and configured.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Store Context Integration", "description": "Implement store context integration to identify the store based on the incoming phone number.", "details": "Modify the `ZurnoAIReceptionistService.welcome()` method to identify the store using the `toNumber` from the Twilio request. Query the `ZnStore` model using the phone number. Load store-specific context, including services, packages, and working hours. Store the store ID in the `ZnCall` record. Implement error handling for unsupported stores.", "testStrategy": "Unit tests to verify store identification logic. Integration tests to ensure correct store context loading and handling of invalid phone numbers.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Customer Management Integration", "description": "Implement customer management integration to identify and create customer records.", "details": "Implement the `findOrCreateCustomer` method to check if a customer exists in the `ZnUser` table based on the phone number. If the customer doesn't exist, create a new customer record using the provided name. Link the customer to the store using the `customerStores` relationship. Implement placeholder email generation. Ensure proper data validation.", "testStrategy": "Unit tests to verify customer lookup and creation. Integration tests to ensure customer-store linking and handling of missing customer names.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "AI Conversation Processing and Intent Recognition", "description": "Implement AI conversation processing with booking intent recognition.", "details": "Enhance the `handleReception` method to load store and customer context. Build a system prompt for OpenAI, including store information and customer context. Call the OpenAI API to process the conversation. Parse the AI response for booking intent. If booking intent is detected, call the `processBookingIntent` method.", "testStrategy": "Unit tests to verify prompt generation and AI response parsing. Integration tests to validate conversation flow and intent recognition.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Basic Appointment Creation", "description": "Implement basic appointment creation from successful AI conversations.", "details": "Implement the `processBookingIntent` method. Find or create the customer. Find the requested service. Check for basic availability using the `checkBasicAvailability` method. Create an appointment using the `AppointmentService`. Update the `ZnCall` record with the appointment ID. Implement error handling for various scenarios.", "testStrategy": "Unit tests to verify appointment creation logic and availability checks. Integration tests to validate end-to-end booking flows.", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Database Schema Updates", "description": "Update the database schema to include appointment tracking in call records.", "details": "Create a migration to add an `appointmentId` column to the `zn_calls` table. Define a foreign key relationship to the `zn_appointments` table. Implement the migration and ensure it runs successfully.", "testStrategy": "Verify the database schema changes by inspecting the database structure after the migration.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Natural Language Processing Enhancement", "description": "Enhance natural language processing to handle complex date/time expressions and service variations.", "details": "Implement logic to parse complex date and time expressions (e.g., \"next Monday morning\"). Implement logic to understand service variations (e.g., \"mani-pedi\"). Handle multi-service bookings. Extract customer preferences and special requests.", "testStrategy": "Unit tests to verify date/time parsing and service recognition. Integration tests to validate booking flows with complex requests.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 8, "title": "Intelligent Availability Management", "description": "Implement intelligent availability management with smart time slot suggestions.", "details": "Implement smart time slot suggestions based on service duration. Implement buffer time management between appointments. Consider staff availability integration (future). Handle seasonal/peak time handling.", "testStrategy": "Unit tests to verify time slot suggestions and buffer time calculations. Integration tests to validate availability management under different scenarios.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 9, "title": "Enhanced Customer Experience", "description": "Enhance the customer experience with customer history integration and preference learning.", "details": "Integrate customer history (e.g., \"your usual service\"). Implement preference learning and suggestions. Consider loyalty program integration. Implement appointment reminders and follow-ups.", "testStrategy": "Unit tests to verify customer history retrieval and preference suggestions. Integration tests to validate the enhanced customer experience.", "priority": "low", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 10, "title": "Advanced Booking Scenarios", "description": "Implement support for advanced booking scenarios, such as package booking and recurring appointments.", "details": "Implement package booking support. Implement group appointments. Implement recurring appointment setup. Implement modification and cancellation support.", "testStrategy": "Unit tests to verify package booking and recurring appointment setup. Integration tests to validate advanced booking scenarios.", "priority": "low", "dependencies": [5], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-03T14:40:50.052Z", "updated": "2025-07-03T14:40:50.052Z", "description": "Tasks for master context"}}}
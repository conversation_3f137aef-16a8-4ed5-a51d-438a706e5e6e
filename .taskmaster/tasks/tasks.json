{"master": {"tasks": [{"id": 1, "title": "Project Setup and Environment Configuration", "description": "Set up the project repository and initial development environment for the AI Receptionist integration.", "details": "Create a new repository (e.g., in GitHub, GitLab). Initialize the project with necessary dependencies (Node.js, TypeScript, relevant SDKs for Twilio, OpenAI, and database access). Configure the development environment, including linting, formatting, and testing tools.", "testStrategy": "Verify the environment setup by running basic tests and ensuring all dependencies are correctly installed and configured.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Create Project Repository and Initialize Project", "description": "Create a new project repository on a platform like GitHub or GitLab. Initialize the project with a `package.json` file, setting up the project name, version, and entry point.  Choose a suitable license (e.g., MIT).", "dependencies": [], "details": "Use the platform's web interface or CLI to create the repository.  Run `npm init -y` to create the `package.json` file.  Consider using a `.gitignore` file to exclude unnecessary files (e.g., `node_modules`).", "status": "done", "testStrategy": "Verify the repository creation and basic project initialization by checking the presence of the `.git` directory and `package.json` file."}, {"id": 2, "title": "Install Core Dependencies and Configure TypeScript", "description": "Install Node.js, TypeScript, and related packages. Configure TypeScript by creating a `tsconfig.json` file, specifying compiler options such as target (ES2020), module (CommonJS or ESNext), and strict mode.  Also install a package manager like npm or yarn.", "dependencies": [1], "details": "Run `npm install --save-dev typescript @types/node` to install TypeScript and its type definitions.  Create a `tsconfig.json` file using `tsc --init` and customize it.  Install a package manager like npm or yarn to manage dependencies.", "status": "done", "testStrategy": "Verify TypeScript compilation by creating a simple `.ts` file and compiling it using `tsc`. Check for the generated `.js` file."}, {"id": 3, "title": "Install and Configure SDKs and Database Access", "description": "Install the necessary SDKs for Twilio, OpenAI, and database access. Configure these SDKs with appropriate API keys and connection strings.  Choose a database (e.g., MongoDB, PostgreSQL) and install its client library.", "dependencies": [2], "details": "Install the required SDKs using npm (e.g., `@twilio/runtime`, `openai`, `mongodb` or `pg`).  Obtain API keys and connection strings from the respective services.  Create configuration files or environment variables to store sensitive information securely.", "status": "done", "testStrategy": "Test the SDK installations by attempting to connect to each service using the provided credentials.  Verify successful connections and data retrieval (e.g., listing available Twilio phone numbers)."}, {"id": 4, "title": "Set Up Linting and Formatting Tools", "description": "Integrate linting and formatting tools (e.g., ESLint, <PERSON><PERSON>er) into the project. Configure these tools to enforce code style guidelines and automatically format code.  Create configuration files for these tools.", "dependencies": [2], "details": "Install ESLint and Prettier as dev dependencies.  Create `.eslintrc.js` and `.prettierrc.js` files to define the linting and formatting rules.  Integrate these tools into the development workflow (e.g., using VS Code extensions or pre-commit hooks).", "status": "done", "testStrategy": "Test linting and formatting by intentionally introducing code style violations and running the linters and formatters. Verify that the tools identify and automatically fix the violations."}, {"id": 5, "title": "Configure Testing Framework and Initial Tests", "description": "Set up a testing framework (e.g., <PERSON><PERSON>, <PERSON><PERSON>) and write initial unit tests. Configure the testing environment and define test scripts in `package.json`.  Create a basic test suite to verify core functionalities.", "dependencies": [4], "details": "Install a testing framework (e.g., `npm install --save-dev jest`). Configure Jest (or the chosen framework) by creating a configuration file (e.g., `jest.config.js`). Write basic unit tests for core functions (e.g., functions related to Twilio or OpenAI interactions).  Define test scripts in the `package.json` file (e.g., `npm test`).", "status": "done", "testStrategy": "Run the test suite and verify that the initial tests pass.  Introduce intentional errors in the code and re-run the tests to ensure that the testing framework correctly identifies the failures."}]}, {"id": 2, "title": "Store Context Integration", "description": "Implement store context integration to identify the store based on the incoming phone number.", "details": "Modify the `ZurnoAIReceptionistService.welcome()` method to identify the store using the `toNumber` from the Twilio request. Query the `ZnStore` model using the phone number. Load store-specific context, including services, packages, and working hours. Store the store ID in the `ZnCall` record. Implement error handling for unsupported stores.", "testStrategy": "Unit tests to verify store identification logic. Integration tests to ensure correct store context loading and handling of invalid phone numbers.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": [{"id": 1, "title": "Extract Phone Number from <PERSON><PERSON><PERSON>quest", "description": "Extract the `toNumber` from the incoming Twilio request within the `ZurnoAIReceptionistService.welcome()` method.", "dependencies": [], "details": "Access the request object within the `welcome()` method and retrieve the phone number from the appropriate field (e.g., `req.body.To`). Ensure proper handling of potential null or undefined values.", "status": "done", "testStrategy": "Unit test to verify the correct extraction of the phone number from a mock Twilio request."}, {"id": 2, "title": "Query ZnStore Model by Phone Number", "description": "Implement a query to the `ZnStore` model using the extracted phone number to identify the corresponding store.", "dependencies": [], "details": "Use the extracted phone number as the search criteria in a database query against the `ZnStore` model. Handle cases where no store is found (e.g., return null or throw an exception). Consider using a database index on the phone number field for performance.", "status": "done", "testStrategy": "Unit test to verify the correct store is retrieved given a valid phone number and that null is returned for an invalid number."}, {"id": 3, "title": "Load Store-Specific Context", "description": "Load store-specific context, including services, packages, and working hours, based on the identified store.", "dependencies": [], "details": "If a store is found, retrieve the necessary context data (services, packages, working hours) from the database or other data sources. Structure the context data in a way that's easily accessible within the application. Handle cases where context data is missing or incomplete.", "status": "done", "testStrategy": "Unit test to verify that the correct context data is loaded for a given store ID. Test for cases where the store ID is invalid or context data is missing."}, {"id": 4, "title": "Store Store ID in ZnCall Record", "description": "Store the identified store ID in the `ZnCall` record.", "dependencies": [], "details": "Update the `ZnCall` record with the store ID. Ensure the `ZnCall` model has a field to store the store ID. This should happen after the store is identified but before any further processing of the call.", "status": "done", "testStrategy": "Unit test to verify that the store ID is correctly stored in the `ZnCall` record after a successful store lookup."}, {"id": 5, "title": "Implement Error Handling for Unsupported Stores", "description": "Implement error handling for cases where the phone number does not correspond to a supported store.", "dependencies": [4], "details": "If no store is found based on the phone number, return an appropriate error response or redirect the call to a default handling mechanism. Log the error for debugging purposes. Consider providing a user-friendly message to the caller.", "status": "done", "testStrategy": "Unit test to verify that the correct error handling is triggered when an invalid phone number is provided. Test the error message and logging."}]}, {"id": 3, "title": "Customer Management Integration", "description": "Implement customer management integration to identify and create customer records.", "details": "Implement the `findOrCreateCustomer` method to check if a customer exists in the `ZnUser` table based on the phone number. If the customer doesn't exist, create a new customer record using the provided name. Link the customer to the store using the `customerStores` relationship. Implement placeholder email generation. Ensure proper data validation.", "testStrategy": "Unit tests to verify customer lookup and creation. Integration tests to ensure customer-store linking and handling of missing customer names.", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "AI Conversation Processing and Intent Recognition", "description": "Implement AI conversation processing with booking intent recognition.", "details": "Enhance the `handleReception` method to load store and customer context. Build a system prompt for OpenAI, including store information and customer context. Call the OpenAI API to process the conversation. Parse the AI response for booking intent. If booking intent is detected, call the `processBookingIntent` method.", "testStrategy": "Unit tests to verify prompt generation and AI response parsing. Integration tests to validate conversation flow and intent recognition.", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Basic Appointment Creation", "description": "Implement basic appointment creation from successful AI conversations.", "details": "Implement the `processBookingIntent` method. Find or create the customer. Find the requested service. Check for basic availability using the `checkBasicAvailability` method. Create an appointment using the `AppointmentService`. Update the `ZnCall` record with the appointment ID. Implement error handling for various scenarios.", "testStrategy": "Unit tests to verify appointment creation logic and availability checks. Integration tests to validate end-to-end booking flows.", "priority": "high", "dependencies": [3, 4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Database Schema Updates", "description": "Update the database schema to include appointment tracking in call records.", "details": "Create a migration to add an `appointmentId` column to the `zn_calls` table. Define a foreign key relationship to the `zn_appointments` table. Implement the migration and ensure it runs successfully.", "testStrategy": "Verify the database schema changes by inspecting the database structure after the migration.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Natural Language Processing Enhancement", "description": "Enhance natural language processing to handle complex date/time expressions and service variations.", "details": "Implement logic to parse complex date and time expressions (e.g., \"next Monday morning\"). Implement logic to understand service variations (e.g., \"mani-pedi\"). Handle multi-service bookings. Extract customer preferences and special requests.", "testStrategy": "Unit tests to verify date/time parsing and service recognition. Integration tests to validate booking flows with complex requests.", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 8, "title": "Intelligent Availability Management", "description": "Implement intelligent availability management with smart time slot suggestions.", "details": "Implement smart time slot suggestions based on service duration. Implement buffer time management between appointments. Consider staff availability integration (future). Handle seasonal/peak time handling.", "testStrategy": "Unit tests to verify time slot suggestions and buffer time calculations. Integration tests to validate availability management under different scenarios.", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 9, "title": "Enhanced Customer Experience", "description": "Enhance the customer experience with customer history integration and preference learning.", "details": "Integrate customer history (e.g., \"your usual service\"). Implement preference learning and suggestions. Consider loyalty program integration. Implement appointment reminders and follow-ups.", "testStrategy": "Unit tests to verify customer history retrieval and preference suggestions. Integration tests to validate the enhanced customer experience.", "priority": "low", "dependencies": [4, 5], "status": "pending", "subtasks": []}, {"id": 10, "title": "Advanced Booking Scenarios", "description": "Implement support for advanced booking scenarios, such as package booking and recurring appointments.", "details": "Implement package booking support. Implement group appointments. Implement recurring appointment setup. Implement modification and cancellation support.", "testStrategy": "Unit tests to verify package booking and recurring appointment setup. Integration tests to validate advanced booking scenarios.", "priority": "low", "dependencies": [5], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-07-03T14:40:50.052Z", "updated": "2025-07-03T14:55:19.372Z", "description": "Tasks for master context"}}}
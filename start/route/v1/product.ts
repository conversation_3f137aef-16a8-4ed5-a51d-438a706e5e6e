import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const ProductsController = () => import('#controllers/app/products_controller')

export default function productRoutes() {
  router
    .group(() => {
      // Authentication
      router
        .group(() => {
          router.post('/restock-notification', [ProductsController, 'restockNotification'])
          router.post('/show', [ProductsController, 'show'])

          router.get('/vendors', [ProductsController, 'listVendors'])
          router.get('/categories', [ProductsController, 'listProductCategories'])
          router.get('/types', [ProductsController, 'listProductTypes'])
          router.get('/tags', [ProductsController, 'listProductTags'])

          router.post('/', [ProductsController, 'createProduct'])
          router.put('/:id', [ProductsController, 'updateProduct'])
          router.delete('/:id', [ProductsController, 'deleteProduct'])
        })
        .use(middleware.auth())

      // UnAuthentication
      router.get('/', [ProductsController, 'list'])
      router.get('/bundles', [ProductsController, 'bundles'])
      router.get('/bundles-v2', [ProductsController, 'listBundles'])
      router.get('/bundles/:id', [ProductsController, 'bundleDetail'])
      router.get('/:id', [ProductsController, 'detail'])
      router.get('/:id/meta-data', [ProductsController, 'metaDataDetail'])
      router.get('/:id/relate', [ProductsController, 'relate'])
      router.get('/:id/reviews', [ProductsController, 'reviews'])
      router.get('/:id/affiliate-info', [ProductsController, 'getAffiliateInfo'])
      router.get('/:id/videos', [ProductsController, 'showVideos'])
    })
    .prefix('products')
}

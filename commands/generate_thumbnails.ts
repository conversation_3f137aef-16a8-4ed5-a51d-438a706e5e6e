import { MEDIA_TYPE } from "#constants/media";
import ZnPost, { EPostType } from '#models/zn_post';
import { PostService } from '#services/post_service';
import { args, BaseCommand, flags } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class GenerateThumbnails extends BaseCommand {
  static commandName = 'generate:thumbnails'
  static description = 'Generate thumbnails for posts from videos'

  static options: CommandOptions = {
    startApp: true
  }

  private postService = new PostService()

  @args.string({
    required: false,
    description: "Id of Post. Leave empty if updating entire database."
  })
  declare postId: string

  @flags.number({
    description: "Time of thumbnail in seconds.",
    default: 5
  })
  declare seek: number

  @flags.boolean({
    description: "Overwrite thumbnail if exists.",
    default: false
  })
  declare overwrite: boolean

  @flags.boolean({
    alias: 'f',
    description: "Overwrite thumbnail if exists.",
    default: false
  })
  declare force: boolean

  async run() {
    try {
      const forceOverwrite = this.overwrite || this.force

      if (this.postId) {
        this.logger.info('Start generating thumbnail for 1 post')

        await this.postService.generateThumbnailSync({
          postId: this.postId,
          seek: this.seek,
          overwrite: forceOverwrite,
        })

      } else {
        this.logger.info('Start generating thumbnails for entire database')

        const postsQuery = ZnPost.query()
            .whereHas('medias', (mediaQuery) => {
              mediaQuery.where('type', MEDIA_TYPE.VIDEO)
            })
          .where((videoInquiry) => {
            videoInquiry.orWhereIn('type', [EPostType.VIDEO, EPostType.LIVE]).orWhere((postTypeQuery) => {
              postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
                mediaQuery.where('type', MEDIA_TYPE.VIDEO)
              })
            })
          })

        if (!forceOverwrite) {
          postsQuery.whereNull('thumbnailId')
        }

        const posts = await postsQuery

        for (const post of posts) {
          await this.postService.generateThumbnailAsync({
            postId: post.id,
            seek: this.seek,
            overwrite: forceOverwrite,
          })
        }

        this.logger.info("Jobs sent")
      }

    } catch (error) {
      this.logger.error(error)
    }
  }
}

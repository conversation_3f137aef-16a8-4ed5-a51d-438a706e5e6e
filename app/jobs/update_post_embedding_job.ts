import {BaseEmbeddingJob} from "#jobs/base_embedding_job";
import ZnPost from "#models/zn_post";
import PostEmbeddingService from "#services/pinecone/post_embedding_service";

export default class UpdatePostEmbeddingJob extends BaseEmbeddingJob<ZnPost> {
  protected embeddingService = new PostEmbeddingService()

  static get $$filepath() { return import.meta.url }

  protected lookup(ids: string[]) {
    return ZnPost.query()
      .whereIn('id', ids)
      .preload('categories')
      .preload('city')
      .preload('state')
      .preload('store')
      .preload('country')
      .preload('translations')
  }
}


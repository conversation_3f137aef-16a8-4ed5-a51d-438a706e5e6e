import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import ZnPost, { EPostSource, EPostType } from '#models/zn_post'
import ZnPostTranslation from '#models/zn_post_translation'
import ZnStore from '#models/zn_store'
import ZnUser from '#models/zn_user'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import JwtService from '#services/jwt_service'
import { PostService } from '#services/post_service'
import { TranslationModelName } from '#services/translation_model_service'
import TranslationService from '#services/translation_service'
import { createPostValidator, likePostValidator } from '#validators/post'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'
import { TRACKING_ACTION } from '../constants/tracking.js'
import { TRANSLATIONS_ENUM } from '../constants/app.js'
import PostRecommendationService from '#services/post_recommendation_service'
import { CacheResponse } from '../decorators/cache_response.decorator.js'
import { PostPopularityService } from '#services/post_popularity_service'
import { MEDIA_TYPE } from '#constants/media'
import { CacheService } from '#services/cache_service'
import redis from '@adonisjs/redis/services/main'
import queue from '@rlanz/bull-queue/services/main'
import UpdateTrackingSnapshotJob from '#jobs/update_tracking_snapshot_job'
import { TrackingService } from '#services/tracking_service'

export default class PostController {
  private translationService: TranslationService
  private postService: PostService
  private postRecommendationService: PostRecommendationService
  private postPopularityService: PostPopularityService
  // private userReadPreference = new Map()
  // private STICKY_READ_DURATION = 5000 // 5 seconds
  constructor() {
    this.translationService = new TranslationService()
    this.postService = new PostService()
    this.postRecommendationService = new PostRecommendationService()
    this.postPopularityService = new PostPopularityService()
  }

  /**
   * @create
   * @tag Post
   * @summary Create a post
   * @requestBody <ZnPost>.only(title,description,content,storeId,thumbnailId,address,latitude,longitude,phone,zipcode,unlist,source,scheduledAt).append("price":"55000","isDraft":0,"isFavorite":0,"isUnlist":0,"source":"","categoryIds":[],"mediaIds":[],"timelines":[{"variantId":"","start":10,"end":20}])
   * @responseBody 201 - <ZnPost>.append("id":""")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async create({ request, response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser
    const data = request.all()

    try {
      const payload = await createPostValidator.validate(data)
      //Check empty post
      if (
        !payload.title &&
        !payload.description &&
        !payload.thumbnailId &&
        payload?.mediaIds?.length === 0
      ) {
        return response.internalServerError({
          message: 'Please input content',
        })
      }

      let postCondition = null
      if (payload.title) {
        postCondition = { userId: user.id, source: payload.source || null, title: payload.title }
      } else if (payload.description) {
        postCondition = {
          userId: user.id,
          source: payload.source || null,
          description: payload.description,
        }
      }

      const postData = {
        userId: user.id,
        title: payload.title && payload.source !== 'reel' ? payload.title : undefined,
        description: payload.description ?? undefined,
        content: payload.content ?? undefined,
        price: payload.price ? Number(Number.parseFloat(payload.price).toFixed(2)) : undefined,
        storeId: payload.storeId ?? null,
        thumbnailId: payload.thumbnailId ?? undefined,
        isDraft: payload.isDraft !== undefined ? payload.isDraft : (true as any),
        isFavourite: payload.isFavourite !== undefined ? payload.isFavourite : (false as any),
        isUnlist: payload.isUnlist !== undefined ? payload.isUnlist : (false as any),
        address: payload.address ?? undefined,
        latitude: payload.latitude,
        longitude: payload.longitude,
        phone: payload.phone,
        zipcode: payload.zipcode,
        source: payload.source || null,
        type: payload.type || null,
        scheduledAt: payload.scheduledAt ? DateTime.fromJSDate(payload.scheduledAt) : null,
      }

      // If scheduledAt is set, default to draft
      if (postData.scheduledAt) {
        postData.isDraft = true
      }

      let created
      if (postCondition) {
        created = await ZnPost.updateOrCreate(postCondition, postData)
      } else {
        created = await ZnPost.create(postData)
      }

      // Track read preference (force primary for 5s)
      // this.userReadPreference.set(created.id, Date.now() + this.STICKY_READ_DURATION)

      if (payload.mediaIds && payload.mediaIds.length > 0) {
        await created.related('medias').sync(payload.mediaIds)
      }

      if (payload.categoryIds && payload.categoryIds.length > 0) {
        await created.related('categories').sync(payload.categoryIds)
      }

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(created?.id, payload.timelines)
      }

      if (!created.thumbnailId) {
        await this.postService.generateThumbnailAsync({ postId: created.id })
      }

      // Decide whether to use primary or replica DB
      // const shouldReadPrimary = this.userReadPreference.get(created.id) > Date.now()
      // const dbMode = shouldReadPrimary ? { mode: 'write' } : {}

      // @ts-ignore
      const post = await ZnPost.query({ mode: 'write' })
        .where('id', created.id)
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .first()

      const { thumbnail, ...serializedPost } = post!.serialize()
      const medias = thumbnail ? serializedPost.medias.concat(thumbnail) : serializedPost.medias

      return response.created({
        ...serializedPost,
        medias,
      })
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @getRelatedPost
   * @tag Post
   */
  async getRelatedPost({ request, response, params, locale, user }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const postId = params.id

    const post = await ZnPost.query()
      .preload('store', (storeQuery) => {
        storeQuery.preload('logo')
      })
      .preload('categories', (categoryQuery) => {
        categoryQuery.preload('thumbnail')
      })
      .preload('medias')
      .preload('thumbnail')
      .where('id', postId)
      .first()

    if (!post) {
      return response.notFound('Post not found')
    }

    try {
      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        // .whereNull('deletedAt')
        .where('expired', false)
        .whereNot('id', postId)
        .orderByRaw('RAND()')

      const categoryIds = post.categories.map((category) => category.id)

      const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]

      if (categoryIdsArray.length > 0) {
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }

      if (post.store) {
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', [post.store.id])
        })
      }

      const result = await query.paginate(page, limit)

      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )

      data.data = await Promise.all(
        data.data.map(async (post) => {
          const isLike = await this.postService.isUserLikePost(post.id, user?.id)

          return { ...post, isFavourite: isLike }
        })
      )

      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @list
   * @tag Post
   * @summary Read all posts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery source - Post Source - @enum(zurno,reel)
   * @paramQuery categoryIds - IDs of categories (e.g. 952b3bf6-ac02-4f28-b8f4-b98491ae2f39) - @type(array)
   * @paramQuery storeIds - IDs of stores (e.g. 87dc894e-1fe1-4e45-b2cb-e01ea392dba2) - @type(array)
   * @paramQuery isDraft - Draft Status - @type(boolean)
   * @paramQuery isFavourite - Favourite Status - @type(boolean)
   * @paramQuery priceFrom - Price From - @type(float) @example(0.01)
   * @paramQuery priceTo - Price To - @type(float) @example(100)
   * @paramQuery latitude - Search Latitude - @type(float) @example(51.47783)
   * @paramQuery longitude - Search Longitude - @type(float) @example(-0.00139)
   * @paramQuery miles - Miles Limit - @type(number)
   * @paramQuery allCountry - Countries Limit - @type(boolean) example(false)
   * @paramQuery sortBy - Sort By Types - @enum(default,distance,popular,newest)
   * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","medias":["ZnMedia"],"categories":["ZnCategory"],"thumbnail":"ZnMedia","user":"ZnUser").paginated() - Read all posts descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  @CacheResponse(180)
  async list({ request, response, user, locale }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        categoryIds,
        storeIds,
        latitude,
        longitude,
        isDraft = false,
        isFavourite,
        priceFrom,
        priceTo,
        sortBy = 'default',
        miles,
        allCountry = false,
        source,
      } = request.qs()
      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .whereNull('deletedAt')
        .preload('product', (productQuery) => {
          productQuery.preload('variant').preload('image').where('status', 'active')
        })
        .preload('stream')
        .where('expired', false)
        .where('isUnlist', false)

      const lat = Number.parseFloat(latitude)
      const lon = Number.parseFloat(longitude)
      const maxDistance = miles ? Number.parseFloat(miles) : 50
      const sortByMany = Array.isArray(sortBy) ? sortBy : [sortBy]

      //**AI-Powered Search**
      let filteredIds: string[] = []
      if (search || (lat && lon)) {
        if (search && lat && lon) {
          filteredIds = await this.postService.searchByTextWithinLocation({
            search,
            lat: lat ?? 0,
            lon: lon ?? 0,
            miles: maxDistance,
            allCountry,
          })
        } else if (search) {
          filteredIds = await this.postService.searchByText(search)
        } else if (lat && lon) {
          filteredIds = await this.postService.getPostIdsWithinDistance({ lat, lon, miles })
        }
        if (filteredIds.length > 0) {
          query.whereIn('id', filteredIds)
        } else {
          query.whereRaw('1=0')
        }
      }

      if (isDraft) {
        query.where('isDraft', isDraft)
      } else {
        query.where('isDraft', false)
      }
      if (isFavourite === '1') {
        if (!user?.id) {
          query.whereRaw('1=0')
        } else {
          const likedPostIds = await db
            .from('zn_users_like_resources')
            .where({
              resourceType: RESOURCE_TYPE.POST,
              userId: user?.id,
            })
            .select('resourceId')
            .then((records) => records.map((record) => record.resourceId))
          if (likedPostIds.length > 0) {
            query.whereIn('id', likedPostIds)
          } else {
            query.whereRaw('1=0')
          }
        }
      }
      if (categoryIds && categoryIds.length > 0) {
        const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }
      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', storeIdsArray)
        })
      }

      if (source) {
        query.where('source', source)
      } else {
        query.whereNull('source')
      }

      if (priceFrom || priceTo) {
        const parsedPriceFrom = priceFrom ? Number(Number.parseFloat(priceFrom).toFixed(2)) : null
        const parsedPriceTo = priceTo ? Number(Number.parseFloat(priceTo).toFixed(2)) : null
        query.where((priceQuery) => {
          if (parsedPriceFrom !== null) {
            priceQuery.where('price', '>=', parsedPriceFrom)
          }
          if (parsedPriceTo !== null) {
            priceQuery.where('price', '<=', parsedPriceTo)
          }
        })
      }

      if (sortByMany.includes('distance') && lat && lon) {
        query.orderByRaw(
          `(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) ASC`,
          [lat, lon, lat]
        )
      } else if (sortByMany.includes('newest') || source === EPostSource.ZURNO) {
        query.orderBy('updatedAt', 'desc')
      } else if (sortByMany.includes('popular')) {
        // Delegate to the shared popular logic
        return await this.getPopularPosts(request, response, locale)
      } else if (sortByMany.includes('default')) {
        query.orderBy('updatedAt', 'desc')
      } else {
        query.orderByRaw('RAND()')
      }

      const result = await query.paginate(page, limit)
      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )
      data.data = await Promise.all(
        data.data.map(async (post) => {
          let isLike = await this.postService.isUserLikePost(post.id, user?.id)
          return { ...post, isFavourite: isLike }
        })
      )
      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async getPopularPosts(
    request: any,
    response: any,
    locale?: TRANSLATIONS_ENUM
  ): Promise<any> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        categoryIds,
        storeIds,
        latitude,
        longitude,
        isDraft = false,
        isFavourite,
        priceFrom,
        priceTo,
        sortBy = 'popular',
        miles,
        allCountry = false,
        source,
      } = request.qs()

      // Use Eloquent model for consistency with main list method
      const query = ZnPost.query()
        .whereNull('deletedAt')
        .where('expired', false)
        .where('isUnlist', false)

      // Search (AI-powered) and Location filtering
      let filteredIds: string[] = []
      const lat = Number.parseFloat(latitude)
      const lon = Number.parseFloat(longitude)
      const maxDistance = miles ? Number.parseFloat(miles) : 50
      const sortByMany = Array.isArray(sortBy) ? sortBy : [sortBy]

      if (search || (lat && lon)) {
        if (search && lat && lon) {
          filteredIds = await this.postService.searchByTextWithinLocation({
            search,
            lat: lat ?? 0,
            lon: lon ?? 0,
            miles: maxDistance,
            allCountry,
          })
        } else if (search) {
          filteredIds = await this.postService.searchByText(search)
        } else if (lat && lon) {
          // Apply distance filtering when lat/lon are provided (with or without miles parameter)
          filteredIds = await this.postService.getPostIdsWithinDistance({
            lat,
            lon,
            miles: maxDistance,
          })
        }
        if (filteredIds.length > 0) {
          query.whereIn('posts.id', filteredIds)
        } else {
          query.whereRaw('1=0')
        }
      }

      // Draft filter
      if (isDraft) {
        query.where('posts.isDraft', isDraft)
      } else {
        query.where('posts.isDraft', false)
      }

      // Favourite filter
      if (isFavourite === '1') {
        // This is a public endpoint, so we can't filter by user. Just return none.
        query.whereRaw('1=0')
      }

      // Category filter
      if (categoryIds && categoryIds.length > 0) {
        const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }

      // Store filter
      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', storeIdsArray)
        })
      }

      // Source filter
      if (source) {
        query.where('source', source)
      } else {
        query.whereNull('source')
      }

      // Price filter
      if (priceFrom || priceTo) {
        const parsedPriceFrom = priceFrom ? Number(Number.parseFloat(priceFrom).toFixed(2)) : null
        const parsedPriceTo = priceTo ? Number(Number.parseFloat(priceTo).toFixed(2)) : null
        query.where(function (this: any) {
          if (parsedPriceFrom !== null) {
            this.where('price', '>=', parsedPriceFrom)
          }
          if (parsedPriceTo !== null) {
            this.where('price', '<=', parsedPriceTo)
          }
        })
      }

      // Distance sort (if requested)
      if (sortByMany.includes('distance') && lat && lon) {
        query.select(
          '*',
          db.raw(
            `(
              3959 * acos(
                cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) +
                sin(radians(?)) * sin(radians(latitude))
              )
            ) as distance`,
            [lat, lon, lat]
          )
        )
      } else {
        // Basic post selection without popularity calculations
        query.select('*')
      }

      // Get ALL matching posts first (without pagination) to properly sort by popularity
      const allMatchingPosts = await query

      // Get all post IDs
      const allPostIds = allMatchingPosts.map((p: any) => p.id)
      if (allPostIds.length === 0) {
        return response.ok({
          meta: {
            total: 0,
            perPage: limit,
            currentPage: page,
            lastPage: 0,
            firstPage: 1,
            firstPageUrl: `/?page=1`,
            lastPageUrl: `/?page=0`,
            nextPageUrl: null,
            previousPageUrl: null,
          },
          data: [],
        })
      }

      // Get user ID if available
      let requestUserId: string | undefined
      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      // Get popularity scores for ALL matching posts
      const [popularityScores, userInteractions] = await Promise.all([
        this.postPopularityService.getPostsPopularityScores(allPostIds),
        requestUserId
          ? this.postPopularityService.getUserPostInteractions(requestUserId, allPostIds)
          : ({} as Record<string, boolean>),
      ])

      // Combine posts with their popularity scores and sort by popularity
      const postsWithPopularity = allMatchingPosts.map((post: any) => {
        const popularity = popularityScores[post.id] || {
          commentCount: 0,
          likeCount: 0,
          viewCount: 0,
          popularityScore: 0,
        }

        return {
          ...post,
          ...popularity,
          isFavourite: Number(userInteractions[post.id] || false),
        }
      })

      // Sort by popularity score (descending)
      postsWithPopularity.sort((a, b) => b.popularityScore - a.popularityScore)

      // Calculate pagination
      const total = postsWithPopularity.length
      const lastPage = Math.ceil(total / limit)
      const startIndex = (page - 1) * limit
      const endIndex = startIndex + limit
      const paginatedPosts = postsWithPopularity.slice(startIndex, endIndex)

      // Get the post IDs for the current page
      const pagePostIds = paginatedPosts.map((p: any) => p.id)

      // Preload relations for the current page posts only
      const postModels = await ZnPost.query()
        .whereIn('id', pagePostIds)
        .orderByRaw(`FIELD(id, ${pagePostIds.map(() => '?').join(',')})`, pagePostIds)
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')

      // Map the final result with all data
      const result = postModels.map((post) => {
        const postWithPopularity = paginatedPosts.find((p: any) => p.id === post.id)
        const distance = postWithPopularity?.distance

        return {
          ...post.serialize(),
          commentCount: postWithPopularity?.commentCount || 0,
          likeCount: postWithPopularity?.likeCount || 0,
          viewCount: postWithPopularity?.viewCount || 0,
          popularityScore: postWithPopularity?.popularityScore || 0,
          distance: distance !== undefined ? Number(distance) : undefined,
          isFavourite: postWithPopularity?.isFavourite || 0,
        }
      })

      // Apply translations if locale is provided
      let finalResult = result
      if (locale) {
        finalResult = await Promise.all(
          result.map(async (post) => {
            return await this.translationService.singleModel(
              TranslationModelName.post,
              post,
              locale
            )
          })
        )
      }

      // Create pagination metadata
      const paginated = {
        meta: {
          total,
          perPage: limit,
          currentPage: page,
          lastPage,
          firstPage: 1,
          firstPageUrl: `/?page=1`,
          lastPageUrl: `/?page=${lastPage}`,
          nextPageUrl: page < lastPage ? `/?page=${page + 1}` : null,
          previousPageUrl: page > 1 ? `/?page=${page - 1}` : null,
        },
        data: finalResult,
      }
      return response.ok(paginated)
    } catch (error) {
      console.error(error)
      const { page = 1, limit = 10 } = request.qs()
      return response.ok({
        meta: {
          total: 0,
          perPage: limit,
          currentPage: page,
          lastPage: 0,
          firstPage: 1,
          firstPageUrl: `/?page=1`,
          lastPageUrl: `/?page=0`,
          nextPageUrl: null,
          previousPageUrl: null,
        },
        data: [],
      })
    }
  }

  /**
   * @listVideos
   * @tag Post
   * @summary Read all videos posts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery userId - Id of User - @type(string)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery source - Post Source - @enum(zurno,reel)
   * @paramQuery categoryIds - IDs of categories (e.g. 952b3bf6-ac02-4f28-b8f4-b98491ae2f39) - @type(array)
   * @paramQuery storeIds - IDs of stores (e.g. 87dc894e-1fe1-4e45-b2cb-e01ea392dba2) - @type(array)
   * @paramQuery isDraft - Draft Status - @type(boolean)
   * @paramQuery isFavourite - Favourite Status - @type(boolean)
   * @paramQuery isUnlist - Include unlisted posts - @type(boolean) @example(1)
   * @paramQuery latitude - Search Latitude - @type(float) @example(51.47783)
   * @paramQuery longitude - Search Longitude - @type(float) @example(-0.00139)
   * @paramQuery miles - Miles Limit - @type(number)
   * @paramQuery allCountry - Countries Limit - @type(boolean) example(false)
   * @paramQuery sortBy - Sort By Types - @enum(default,distance,popular,newest)
   * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","medias":["ZnMedia"],"categories":["ZnCategory"],"thumbnail":"ZnMedia","user":"ZnUser").paginated() - Read all posts descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async listVideos({ request, response, user, locale }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10,
        userId,
        search,
        categoryIds,
        storeIds,
        latitude,
        longitude,
        isDraft = false,
        isFavourite,
        isUnlist = '0',
        sortBy = 'default',
        miles,
        allCountry = false,
        source,
      } = request.qs()
      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias', (mediaQuery) => {
          mediaQuery.where('type', MEDIA_TYPE.VIDEO).orderByRaw('RAND()')
        })
        .whereHas('medias', (mediaQuery) => {
          mediaQuery.where('type', MEDIA_TYPE.VIDEO)
        })
        .preload('thumbnail')
        .preload('user')
        .preload('product', (productQuery) => {
          productQuery.preload('variant').preload('image').where('status', 'active')
        })
        .preload('timelines', (timelineQuery) => {
          timelineQuery
            .whereHas('variant', (variantQuery) => {
              variantQuery.whereHas('product', (productQuery) => {
                productQuery.where('status', 'active')
              })
            })
            .preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product')
            })
        })
        .preload('stream')
        .whereNull('deletedAt')
        .where('expired', false)
        .where((videoInquiry) => {
          videoInquiry.orWhereIn('type', [EPostType.VIDEO]).orWhere((postTypeQuery) => {
            postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
              mediaQuery.where('type', MEDIA_TYPE.VIDEO)
            })
          })
        })

      if (isUnlist) {
        query.where('isUnlist', isUnlist === '1')
      }

      const lat = Number.parseFloat(latitude)
      const lon = Number.parseFloat(longitude)
      const maxDistance = miles ? Number.parseFloat(miles) : 50
      // const sortByMany = Array.isArray(sortBy) ? sortBy : [sortBy]

      if (userId) {
        query.where('userId', userId)
        query.orderBy('createdAt', 'desc')
      } else if (sortBy === 'newest') {
        query.orderBy('createdAt', 'desc')
      } else {
        query.orderByRaw('RAND()')
      }

      //**AI-Powered Search**
      let filteredIds: string[] = []
      if (search) {
        filteredIds = await this.postService.searchByTextWithinLocation({
          search,
          lat: lat ?? 0,
          lon: lon ?? 0,
          miles: maxDistance,
          allCountry,
        })
        if (filteredIds.length > 0) {
          query.whereIn('id', filteredIds)
        } else {
          query.whereRaw('1=0')
        }
      }

      if (isDraft) {
        query.where('isDraft', isDraft)
      } else {
        query.where('isDraft', false)
      }

      if (isFavourite === '1') {
        if (!user?.id) {
          query.whereRaw('1=0')
        } else {
          const likedPostIds = await db
            .from('zn_users_like_resources')
            .where({
              resourceType: RESOURCE_TYPE.POST,
              userId: user?.id,
            })
            .select('resourceId')
            .then((records) => records.map((record) => record.resourceId))
          if (likedPostIds.length > 0) {
            query.whereIn('id', likedPostIds)
          } else {
            query.whereRaw('1=0')
          }
        }
      }

      if (categoryIds && categoryIds.length > 0) {
        const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }

      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', storeIdsArray)
        })
      }

      if (source) {
        if (source != 'all') {
          query.where('source', source)
        }
      } else {
        // temporary solution for app
        // query.whereNull('source')
        query.where((sourceQuery) => {
          sourceQuery.whereNull('source').orWhere('source', EPostSource.REEL)
        })
      }

      // if (sortByMany.includes('distance') && lat && lon) {
      //   query.orderByRaw(
      //     `(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) ASC`,
      //     [lat, lon, lat]
      //   )
      // } else if (sortByMany.includes('newest') || source === EPostSource.ZURNO) {
      //   query.orderBy('updatedAt', 'desc')
      // } else if (sortByMany.includes('popular')) {
      //   query.orderBy('isFavourite', 'desc')
      // } else if (sortByMany.includes('default')) {
      //   query.orderByRaw('RAND()')
      // }

      const result = await query.paginate(page, limit)
      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )
      data.data = await Promise.all(
        data.data.map(async (post) => {
          let isLike = await this.postService.isUserLikePost(post.id, user?.id)
          return { ...post, isFavourite: isLike }
        })
      )
      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Post
   * @summary Read a post
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - <ZnPost>.with(store,store.logo,categories,categories.thumbnail,medias,thumbnail,user,product,collection,timelines,timelines.variant,timelines.variant.image,timelines.variant.product,type).append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"mine":false) - Read a post descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  @CacheResponse(120)
  async show({ params, response, request, locale }: HttpContext) {
    try {
      let requestUserId
      const postId = params.id

      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const post = await ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .preload('product', (productQuery) => {
          productQuery.preload('variant').preload('image').where('status', 'active')
        })
        .preload('collection', (collectionQuery) => {
          collectionQuery.where('status', true)
        })
        .preload('timelines', (timelineQuery) => {
          timelineQuery
            .whereHas('variant', (variantQuery) => {
              variantQuery.whereHas('product', (productQuery) => {
                productQuery.where('status', 'active')
              })
            })
            .preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product')
            })
            .preload('refCode')
        })
        .preload('stream')
        .where({
          id: postId,
          expired: false,
        })
        .first()

      if (!post) {
        return response.notFound('Post not found')
      }

      // Check if user has permission to view the post
      const canViewPost = await this.checkPostViewPermission(post, requestUserId)
      if (!canViewPost) {
        return response.notFound('Post not found')
      }

      const isLike = await this.postService.isUserLikePost(post.id, requestUserId)

      const likeCountRow = await db
        .from('zn_users_like_resources')
        .where({ resourceId: post.id, resourceType: RESOURCE_TYPE.POST })
        .count('* as total')
        .first()
      const likeCount = likeCountRow ? Number(likeCountRow.total) : 0

      const { thumbnail, medias, ...serializedPost } = post.serialize()
      // only put thumbnail into medias if medias is empty
      if (medias.length == 0) {
        // put thumbnail into medias if medias doesn't have thumbnail?
        const thumbnailInMedias = medias.find((med: any) => med.id == thumbnail?.id)
        if (thumbnail && !thumbnailInMedias) {
          medias.unshift(thumbnail)
        }
      }
      const model: any = {
        ...serializedPost,
        thumbnail,
        medias,
        mine: post?.userId === requestUserId,
        isFavourite: isLike,
        likeCount,
      }

      const data = await this.translationService.singleModel(
        TranslationModelName.post,
        model,
        locale
      )

      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * Check if user has permission to view the post
   * @param post The post to check
   * @param requestUserId The user ID making the request
   * @returns boolean indicating if user can view the post
   */
  private async checkPostViewPermission(post: ZnPost, requestUserId?: string): Promise<boolean> {
    // If post is not draft or unlist, anyone can view
    if (!post.isDraft && !post.isUnlist) {
      return true
    }

    // if post creator is request user, can view
    if (post.userId === requestUserId) {
      return true
    }

    // if post has a stream that is scheduled, can view
    if (post.isDraft && post.stream?.scheduledAt) {
      return true
    }

    // if post has a scheduledAt date, can view
    if (post.isDraft && post.scheduledAt) {
      return true
    }

    return false
  }

  /**
   * @getNextVideo
   * @tag Post
   * @summary Get next video
   * @paramQuery source - Video source ('zurno') (optional) - @type(string)
   * @paramQuery userId - The ID of posting user (optional) - @type(string)
   * @paramQuery currentPostId - Current post id of video (required if 'source' or 'userID' is provided) - @type(string)
   * @responseBody 200 - <ZnPost>.with(store,store.logo,categories,categories.thumbnail,medias,thumbnail,user,product,collection,timelines,timelines.variant,timelines.variant.image,timelines.variant.product).append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"mine":false) - A post whose type is 'video' and `id` is different from `currentPostId` if any
   * @responseBody 404 - {"message":"Video not found"} - Not Found
   */
  async getNextVideo({ request, response, locale }: HttpContext) {
    try {
      const { currentPostId, source, userId } = request.qs()
      const randomLimit = 100

      let postingUser
      if (userId) {
        postingUser = await ZnUser.find(userId)
      }

      const postQuery = ZnPost.query()
        .select('id')
        .whereHas('medias', (mediaQuery) => {
          mediaQuery.where('type', MEDIA_TYPE.VIDEO)
        })
        .where((videoInquiry) => {
          videoInquiry.orWhereIn('type', [EPostType.VIDEO]).orWhere((postTypeQuery) => {
            postTypeQuery.whereNull('type').whereHas('medias', (mediaQuery) => {
              mediaQuery.where('type', MEDIA_TYPE.VIDEO)
            })
          })
        })
        .where('isDraft', false)
        .where('isUnlist', false)
      if (source) {
        postQuery.where('source', source)
      }
      if (postingUser) {
        postQuery.where('userId', postingUser.id)
      }
      if (source || postingUser) {
        postQuery.orderBy('createdAt', 'desc')
      }
      postQuery.limit(randomLimit)

      const posts = await postQuery
      if (posts.length === 0) {
        return response.notFound('Video not found')
      }

      let nextId
      if (source || postingUser) {
        if (!currentPostId) throw new Error('Current post ID is required')
        const indexOfCurrentPostId = posts.findIndex((post) => post.id === currentPostId)
        nextId =
          indexOfCurrentPostId === -1
            ? posts[0].id
            : posts[(indexOfCurrentPostId + 1) % posts.length].id
      } else {
        const clientId = `${request.ip()}_${request.header('user-agent')}`
        const cacheKey = `recent_posts:${clientId}`
        const RECENT_POSTS_RANGE = 10

        const recentPostIds = await redis.lrange(cacheKey, 0, RECENT_POSTS_RANGE)

        const distantPosts = posts.filter((p) => !recentPostIds.includes(p.id))
        if (distantPosts.length > 0) {
          const index = Math.floor(Math.random() * distantPosts.length)
          nextId = distantPosts[index].id
        } else {
          const index = Math.floor(Math.random() * recentPostIds.length)
          nextId = recentPostIds[index]
        }

        await redis.lpush(cacheKey, nextId)
        await redis.ltrim(cacheKey, 0, RECENT_POSTS_RANGE)
        await redis.expire(cacheKey, 3600)
      }

      const post = await ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias', (mediaQuery) => {
          mediaQuery.where('type', MEDIA_TYPE.VIDEO).orderByRaw('RAND()')
        })
        .preload('thumbnail')
        .preload('user')
        .preload('product', (productQuery) => {
          productQuery.where('status', 'active').preload('variant').preload('image')
        })
        .preload('collection', (collectionQuery) => {
          collectionQuery.where('status', true)
        })
        .preload('timelines', (timelineQuery) => {
          timelineQuery
            .whereHas('variant', (variantQuery) => {
              variantQuery.whereHas('product', (productQuery) => {
                productQuery.where('status', 'active')
              })
            })
            .preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product')
            })
            .preload('refCode')
        })
        .where('id', nextId)
        .firstOrFail()

      let requestUserId
      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const isLike = await this.postService.isUserLikePost(post.id, requestUserId)

      const model: any = {
        ...post?.serialize(),
        mine: post?.userId === requestUserId,
        isFavourite: isLike,
      }

      const data = await this.translationService.singleModel(
        TranslationModelName.post,
        model,
        locale
      )
      return response.ok(data)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Post
   * @summary Update a post
   * @description Update a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @requestBody <ZnPost>.append("price":"200","isDraft":0,"isFavourite":1,"isUnlist":1,"expired":0)
   * @responseBody 200 - <ZnPost>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async update({ request, response, params }: HttpContext) {
    const postId = params.id

    const post = await ZnPost.query().where('id', postId).first()

    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    let requestUserId
    const authToken = request.header('Authorization') as string
    JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
      if (decodedToken) {
        requestUserId = decodedToken.userId
      }
    })
    if (requestUserId != post.userId) {
      return response.forbidden('Forbidden')
    }

    try {
      const data = request.all()

      const payload = await createPostValidator.validate(data)

      post.title = payload.title || post.title
      post.description = payload.description || post.description
      post.content = payload.content || post.content
      post.price = payload.price ? Number(Number.parseFloat(payload.price).toFixed(2)) : post.price
      post.storeId = payload.storeId || post.storeId
      post.thumbnailId = payload.thumbnailId || post.thumbnailId
      post.isDraft =
        payload.isDraft !== undefined && payload.isDraft !== null
          ? payload.isDraft
          : (post.isDraft as any)
      post.isFavourite =
        payload.isFavourite !== undefined && payload.isFavourite !== null
          ? payload.isFavourite
          : (post.isFavourite as any)
      post.address = payload.address || post.address
      post.updatedAt = DateTime.local()
      post.latitude = payload.latitude || post.latitude
      post.longitude = payload.longitude || post.longitude
      post.phone = payload.phone || post.phone
      post.zipcode = payload.zipcode || post.zipcode
      post.expired =
        payload.expired !== undefined && payload.expired !== null
          ? payload.expired
          : (post.expired as any)
      post.isUnlist =
        payload.isUnlist !== undefined && payload.isUnlist !== null
          ? payload.isUnlist
          : (post.isUnlist as any)
      post.scheduledAt = payload.scheduledAt ? DateTime.fromJSDate(payload.scheduledAt) : null

      // If scheduledAt is set, default to draft
      if (post.scheduledAt) {
        post.isDraft = true
      }

      const isRenewCall =
        Object.keys({ ...payload }).length === 1 && Object.keys({ ...payload })[0] === 'name'

      if (isRenewCall) {
        post.notifyExpireAt = null
      }

      if (Array.isArray(payload.mediaIds)) {
        await post.related('medias').sync(payload.mediaIds)
      }

      if (Array.isArray(payload.categoryIds)) {
        await post.related('categories').sync(payload.categoryIds)
      }

      if (payload.timelines) {
        await this.postService.updateOrCreateVideoTimelines(post.id, payload.timelines)
      }

      const updated = await post.save()

      // Invalidate cache for the show method using CacheService
      await CacheService.invalidateResourceCache('posts', postId, authToken)

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Post
   * @summary Soft-delete a post
   * @description Soft-delete a post descriptively
   * @paramPath id - ID of Post - @type(string) @required
   * @responseBody 200 - {"message":"Post soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 403 - Forbidden - Forbidden
   * @responseBody 404 - {"message":"Post not found"} - Not Found
   */
  async softDelete({ params, request, response }: HttpContext) {
    const postId = params.id
    const post = await ZnPost.query().where('id', postId).first()
    if (!post) {
      return response.notFound({ message: 'Post not found' })
    }

    let requestUserId
    const authToken = request.header('Authorization') as string
    JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
      if (decodedToken) {
        requestUserId = decodedToken.userId
      }
    })
    if (requestUserId != post.userId) {
      return response.forbidden('Forbidden')
    }

    try {
      await post.softDelete()

      return response.ok({ message: 'Post soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @statistic
   * @tag Post
   */
  async statistic({ response, auth }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const publishPosts = await ZnPost.query()
        .where('userId', user.id)
        .where('isDraft', 0)
        .count('* as total')
        .first()

      const draftPosts = await ZnPost.query()
        .where('userId', user.id)
        .where('isDraft', 1)
        .count('* as total')
        .first()

      const stores = await ZnStore.query().where('userId', user.id).count('* as total').first()

      return {
        publishPosts: publishPosts?.$extras['total'] || 0,
        draftPosts: draftPosts?.$extras['total'] || 0,
        stores: stores?.$extras['total'] || 0,
      }
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getMyPost
   * @tag Post
   */
  async getMyPost({ request, response, auth, locale }: HttpContext) {
    // @ts-ignore
    const user = auth.getUserOrFail().serialize() as ZnUser

    try {
      const { page = 1, limit = 10, storeIds, isDraft, isUnlist } = request.qs()

      const query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .where('userId', user.id)
        // .whereNull('deletedAt')
        .orderBy('createdAt', 'desc')

      if (isDraft) {
        query.where('isDraft', isDraft).whereNull('deletedAt')
      }

      if (isUnlist) {
        query.where('isUnlist', isUnlist).whereNull('deletedAt')
      }

      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]

        query
          .whereHas('store', (storeQuery) => {
            storeQuery.whereIn('zn_stores.id', storeIdsArray)
          })
          .whereNull('deletedAt')
      }

      const result = await query.paginate(page, limit)

      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )

      data.data = await Promise.all(
        data.data.map(async (post) => {
          const isLike = await this.postService.isUserLikePost(post.id, user.id)

          return { ...post, isFavourite: isLike }
        })
      )

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @likePost
   * @tag Post
   */
  public async likePost({ request, response, params }: HttpContext) {
    const data = request.all()
    const trackingService = new TrackingService() // Add this

    try {
      let resourceId: string
      let resourceType: string

      // Support old app version, remove later
      if (params.id) {
        resourceId = params.id
        resourceType = RESOURCE_TYPE.POST
      } else {
        const payload = await likePostValidator.validate(data)
        resourceId = payload.resourceId as string
        resourceType = payload.resourceType as string
      }

      let userId: string | undefined
      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          userId = decodedToken.userId
        }
      })

      if (!userId) {
        return response.unauthorized({ message: 'Invalid or missing token' })
      }

      const existingLike = await db
        .from('zn_users_like_resources')
        .where({ resourceId, userId, resourceType })
        .first()

      if (existingLike) {
        return response.conflict({ message: 'You have already liked this resource' })
      }

      await Promise.all([
        ZnUserLikeResource.create({ resourceId, userId, resourceType }),
        db
          .from('zn_resource_interacts')
          .where({ resourceId, resource: RESOURCE_TYPE.POST })
          .increment('likeCount', 1),
        // ADD THIS: Update snapshot with user data
        queue.dispatch(
          UpdateTrackingSnapshotJob,
          {
            resourceId,
            resource: trackingService.getResourceByAction(TRACKING_ACTION.ADD_WISHLIST),
            action: TRACKING_ACTION.ADD_WISHLIST,
            userId: userId,
            countChange: 1, // Positive for like
          },
          {
            queueName: 'tracking',
          }
        ),
      ])

      return response.ok({ message: 'Resource liked successfully' })
    } catch (error) {
      console.error(error)
      return response.internalServerError({ message: 'Something went wrong', error })
    }
  }

  /**
   * @dislikePost
   * @tag Post
   */
  public async dislikePost({ request, response, params }: HttpContext) {
    const data = request.all()
    const trackingService = new TrackingService() // Add this

    try {
      let resourceId: string
      let resourceType: string

      // Support old app version, remove later
      if (params.id) {
        resourceId = params.id
        resourceType = 'POST'
      } else {
        const payload = await likePostValidator.validate(data)
        resourceId = payload.resourceId as string
        resourceType = payload.resourceType as string
      }

      let userId: string | undefined
      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          userId = decodedToken.userId
        }
      })

      if (!userId) {
        return response.unauthorized({ message: 'Invalid or missing token' })
      }

      const existingLike = await db
        .from('zn_users_like_resources')
        .where({ resourceId, userId, resourceType })
        .first()

      if (!existingLike) {
        return response.conflict({ message: 'You have not liked this resource' })
      }

      await Promise.all([
        db.from('zn_users_like_resources').where({ resourceId, userId, resourceType }).delete(),
        db
          .from('zn_trackings')
          .where({ resourceId, userId, action: TRACKING_ACTION.ADD_WISHLIST })
          .delete(),
        db
          .from('zn_resource_interacts')
          .where({ resourceId, resource: 'ZnPost' })
          .decrement('likeCount', 1),
        // ADD THIS: Update snapshot to remove user data
        queue.dispatch(
          UpdateTrackingSnapshotJob,
          {
            resourceId,
            resource: trackingService.getResourceByAction(TRACKING_ACTION.ADD_WISHLIST),
            action: TRACKING_ACTION.ADD_WISHLIST,
            userId: userId,
            countChange: -1, // Negative for unlike
          },
          {
            queueName: 'tracking',
          }
        ),
      ])

      return response.ok({ message: 'Resource disliked successfully' })
    } catch (error) {
      console.error(error)
      return response.internalServerError({ message: 'Something went wrong', error })
    }
  }

  async getLikedPostIds(userId: string): Promise<string[]> {
    return await db
      .from('zn_users_like_resources')
      .where({ resourceType: RESOURCE_TYPE.POST, userId })
      .select('resourceId')
  }

  /**
   * @popular
   * @tag Post
   * @summary Get popular posts based on comment, like, and view counts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery source - Post Source - @enum(zurno,reel)
   * @paramQuery categoryIds - IDs of categories (e.g. 952b3bf6-ac02-4f28-b8f4-b98491ae2f39) - @type(array)
   * @paramQuery storeIds - IDs of stores (e.g. 87dc894e-1fe1-4e45-b2cb-e01ea392dba2) - @type(array)
   * @paramQuery isDraft - Draft Status - @type(boolean)
   * @paramQuery isFavourite - Favourite Status - @type(boolean)
   * @paramQuery priceFrom - Price From - @type(float) @example(0.01)
   * @paramQuery priceTo - Price To - @type(float) @example(100)
   * @paramQuery latitude - Search Latitude - @type(float) @example(51.47783)
   * @paramQuery longitude - Search Longitude - @type(float) @example(-0.00139)
   * @paramQuery miles - Miles Limit - @type(number)
   * @paramQuery allCountry - Countries Limit - @type(boolean) example(false)
   * @paramQuery sortBy - Sort By Types - @enum(default,distance,popular,newest)
   * @responseBody 200 - <ZnPost[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","medias":["ZnMedia"],"categories":["ZnCategory"],"thumbnail":"ZnMedia","user":"ZnUser").paginated() - Read all posts descriptively
   */
  async popular({ request, response }: HttpContext) {
    return this.getPopularPosts(request, response)
  }

  async showMetadata({ params, response, user }: HttpContext) {
    try {
      const postId = params.id

      const post = await ZnPost.query()
        .preload('medias')
        .preload('thumbnail')
        .where({
          id: postId,
          isUnlist: false,
          expired: false,
        })
        .first()

      if (!post) {
        return response.notFound('Post not found')
      }

      // Check if user has permission to view the post
      const canViewPost = await this.checkPostViewPermission(post, user?.id)
      if (!canViewPost) {
        return response.notFound('Post not found')
      }

      return response.ok(post)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getForYouPosts
   * @tag Post
   * @summary Get personalized recommended posts for the user (For You)
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnPost[]>.paginated() - Personalized recommended posts
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async getForYouPosts({ request, response, user, locale }: HttpContext) {
    try {
      let requestUserId: string | undefined
      const authToken = request.header('Authorization') as string
      JwtService.decodeToken(authToken, (decodedToken: Partial<{ userId: string }>) => {
        if (decodedToken) {
          requestUserId = decodedToken.userId
        }
      })

      const {
        page = 1,
        limit = 10,
        search,
        categoryIds,
        storeIds,
        latitude,
        longitude,
        isDraft = false,
        isFavourite,
        priceFrom,
        priceTo,
        sortBy = 'default',
        miles,
        allCountry = false,
        source,
      } = request.qs()

      const sortByMany = Array.isArray(sortBy) ? sortBy : [sortBy]
      let lat: number | undefined = undefined
      let lon: number | undefined = undefined
      if (sortByMany.includes('distance')) {
        lat = !isNaN(Number(latitude)) ? Number(latitude) : 27.8816235
        lon = !isNaN(Number(longitude)) ? Number(longitude) : -82.7283456
      } else {
        lat = !isNaN(Number(latitude)) ? Number(latitude) : undefined
        lon = !isNaN(Number(longitude)) ? Number(longitude) : undefined
      }
      const maxDistance = miles ? Number.parseFloat(miles) : 50

      if (!requestUserId) {
        let guestQuery = ZnPost.query()
          .preload('store', (storeQuery) => {
            storeQuery.preload('logo')
          })
          .preload('categories', (categoryQuery) => {
            categoryQuery.preload('thumbnail')
          })
          .preload('medias')
          .preload('thumbnail')
          .preload('user')
          .whereNull('deletedAt')
          .preload('product', (productQuery) => {
            productQuery.preload('variant').preload('image').where('status', 'active')
          })
          .preload('stream')
          .where('expired', false)
          .where('isUnlist', false)
          .where('isDraft', false)

        let filteredIds: string[] = []
        if (search) {
          filteredIds = await this.postService.searchByTextWithinLocation({
            search,
            lat: lat ?? 0,
            lon: lon ?? 0,
            miles: maxDistance,
            allCountry,
          })
          if (filteredIds.length > 0) {
            guestQuery.whereIn('id', filteredIds)
          } else {
            guestQuery.whereRaw('1=0')
          }
        }
        if (categoryIds && categoryIds.length > 0) {
          const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
          guestQuery.whereHas('categories', (categoryQuery) => {
            categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
          })
        }
        if (storeIds && storeIds.length > 0) {
          const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
          guestQuery.whereHas('store', (storeQuery) => {
            storeQuery.whereIn('zn_stores.id', storeIdsArray)
          })
        }
        if (source) {
          guestQuery.where('source', source)
        } else {
          guestQuery.whereNull('source')
        }
        if (priceFrom || priceTo) {
          const parsedPriceFrom = priceFrom ? Number(Number.parseFloat(priceFrom).toFixed(2)) : null
          const parsedPriceTo = priceTo ? Number(Number.parseFloat(priceTo).toFixed(2)) : null
          guestQuery.where((priceQuery) => {
            if (parsedPriceFrom !== null) {
              priceQuery.where('price', '>=', parsedPriceFrom)
            }
            if (parsedPriceTo !== null) {
              priceQuery.where('price', '<=', parsedPriceTo)
            }
          })
        }
        const guestSortByMany = Array.isArray(sortBy) ? sortBy : [sortBy]
        if (guestSortByMany.includes('distance') && lat && lon) {
          guestQuery.orderByRaw(
            `(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) ASC`,
            [lat ?? 0, lon ?? 0, lat ?? 0]
          )
        } else if (guestSortByMany.includes('newest')) {
          guestQuery.orderBy('updatedAt', 'desc')
        } else if (guestSortByMany.includes('popular')) {
          guestQuery.orderBy('isFavourite', 'desc')
        } else if (guestSortByMany.includes('default')) {
          guestQuery.orderByRaw('RAND()')
        }

        const guestResult = await guestQuery.paginate(page, limit)
        const guestData = await this.translationService.modelsWithPagination(
          TranslationModelName.post,
          guestResult,
          locale
        )
        guestData.data = await Promise.all(
          guestData.data.map(async (post) => {
            let isLike = false
            return { ...post, isFavourite: isLike }
          })
        )
        return response.ok(guestData)
      }

      let recommendedIds: string[] = []
      if (requestUserId) {
        const recResult = await this.postRecommendationService.getForYouPosts(requestUserId)
        recommendedIds = recResult.recommendedIds
      }
      if (!recommendedIds.length) {
        return response.ok({
          meta: { total: 0, perPage: limit, currentPage: page, lastPage: 1 },
          data: [],
        })
      }

      let query = ZnPost.query()
        .preload('store', (storeQuery) => {
          storeQuery.preload('logo')
        })
        .preload('categories', (categoryQuery) => {
          categoryQuery.preload('thumbnail')
        })
        .preload('medias')
        .preload('thumbnail')
        .preload('user')
        .whereNull('deletedAt')
        .preload('product', (productQuery) => {
          productQuery.preload('variant').preload('image').where('status', 'active')
        })
        .preload('stream')
        .where('expired', false)
        .where('isUnlist', false)
        .whereIn('id', recommendedIds)

      let filteredIds: string[] = []
      if (search) {
        filteredIds = await this.postService.searchByTextWithinLocation({
          search,
          lat: lat ?? 0,
          lon: lon ?? 0,
          miles: maxDistance,
          allCountry,
        })
        if (filteredIds.length > 0) {
          query.whereIn('id', filteredIds)
        } else {
          query.whereRaw('1=0')
        }
      }

      if (sortByMany.includes('distance') && lat && lon) {
        query.orderByRaw(
          `(3959 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) ASC`,
          [lat ?? 0, lon ?? 0, lat ?? 0]
        )
      } else if (sortByMany.includes('newest')) {
        query.orderBy('updatedAt', 'desc')
      } else if (sortByMany.includes('popular')) {
        query.orderBy('isFavourite', 'desc')
      } else if (sortByMany.includes('default')) {
        query.orderByRaw('RAND()')
      }

      if (isDraft) {
        query.where('isDraft', isDraft)
      } else {
        query.where('isDraft', false)
      }

      if (isFavourite) {
        if (user?.id) {
          const likedPostIds = await this.getLikedPostIds(user?.id)
          const postIds = likedPostIds.map((item: any) => item.resourceId)
          query.whereIn('id', postIds)
        }
      }
      if (categoryIds && categoryIds.length > 0) {
        const categoryIdsArray = Array.isArray(categoryIds) ? categoryIds : [categoryIds]
        query.whereHas('categories', (categoryQuery) => {
          categoryQuery.whereIn('zn_post_categories.id', categoryIdsArray)
        })
      }
      if (storeIds && storeIds.length > 0) {
        const storeIdsArray = Array.isArray(storeIds) ? storeIds : [storeIds]
        query.whereHas('store', (storeQuery) => {
          storeQuery.whereIn('zn_stores.id', storeIdsArray)
        })
      }

      if (source) {
        query.where('source', source)
      } else {
        query.whereNull('source')
      }

      if (priceFrom || priceTo) {
        const parsedPriceFrom = priceFrom ? Number(Number.parseFloat(priceFrom).toFixed(2)) : null
        const parsedPriceTo = priceTo ? Number(Number.parseFloat(priceTo).toFixed(2)) : null
        query.where((priceQuery) => {
          if (parsedPriceFrom !== null) {
            priceQuery.where('price', '>=', parsedPriceFrom)
          }
          if (parsedPriceTo !== null) {
            priceQuery.where('price', '<=', parsedPriceTo)
          }
        })
      }

      if (recommendedIds.length > 0) {
        if (sortBy === 'default') {
          const formattedIds = recommendedIds.map((id) => `'${id}'`).join(', ')
          query.orderByRaw(`FIELD(id, ${formattedIds})`)
        }
      }

      if (search) {
        const postIds = await this.postService.searchWithinDistanceInList({
          search,
          lat,
          lon,
          miles: maxDistance,
          allCountry,
          ids: recommendedIds,
        })
        const formattedIds = postIds.map((id) => `'${id}'`).join(', ')
        if (postIds.length > 0) {
          query.whereIn('id', postIds).orderByRaw(`FIELD(id, ${formattedIds})`)
        } else {
          const translations = await ZnPostTranslation.query().whereRaw(
            'LOWER(value) LIKE LOWER(?)',
            [`%${search}%`]
          )
          query.where(async (queryBuilder) => {
            queryBuilder
              .whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
              .orWhereRaw('LOWER(description) LIKE LOWER(?)', [`%${search}%`])
              .orWhereIn(
                'id',
                translations.map((translation) => translation.postId)
              )
          })
        }
      }

      if (source == EPostSource.ZURNO) {
        query.orderBy('updatedAt', 'desc')
      }
      await this.postService.applyMultiSort(query, lat, lon, sortByMany)
      if (sortBy === 'default') {
        query.orderByRaw('RAND()')
      }

      const result = await query.paginate(page, limit)
      const data = await this.translationService.modelsWithPagination(
        TranslationModelName.post,
        result,
        locale
      )
      data.data = await Promise.all(
        data.data.map(async (post) => {
          let isLike = await this.postService.isUserLikePost(post.id, user?.id)
          return { ...post, isFavourite: isLike }
        })
      )
      return response.ok(data)
    } catch (error) {
      return response.ok({
        meta: { total: 0, perPage: 10, currentPage: 1, lastPage: 1 },
        data: [],
      })
    }
  }
}

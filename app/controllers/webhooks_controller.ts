import { shopifyConfig } from '#config/shopify'
import ProductUpdateNotificationJob from '#jobs/product_update_notification_job'
import DeleteDraftOrderByIdJob from '#jobs/shopify/delete_draft_order_by_id_job'
import StreamEndBroadcastJob from '#jobs/stream_end_broadcast_job'
import SyncShopifyWebhookOrderJob from '#jobs/sync_shopify_webhook_order_job'
import UpdateInventoryJob from '#jobs/update_inventory_job'
import WebhookShopifyProductsJob from '#jobs/webhook_shopify_products_job'
import SaleOrderVoidedNotification from '#mails/shop/sale_order_voided_notification'
import { NotificationService } from '#services/notification_service'
import { OrderService } from '#services/shop/order_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import { ShopifyNotificationService } from '#services/shopify/webhooks/shopify_notification_service'
import type { HttpContext } from '@adonisjs/core/http'
import mail from '@adonisjs/mail/services/main'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import { createHmac } from 'node:crypto'

export default class WebhooksController {
  SHOPIFY_CLIENT_SECRET = shopifyConfig.webhookClientKey
  FUFIL_CLIENT_SECRET = shopifyConfig.fulfilClientKey
  notificationService = new NotificationService()
  // syncOrderService = new SyncOrderService()
  orderService = new OrderService()

  async shopify({ request, response }: HttpContext) {
    // @ts-ignore
    const digest = createHmac('SHA256', this.SHOPIFY_CLIENT_SECRET)
      .update(request.raw() as string, 'utf8')
      .digest('base64')
    const hmac = request.header('X-Shopify-Hmac-Sha256')

    //Catch event from shopify
    if (hmac === digest) {
      const topic = request.header('x-shopify-topic')
      console.log('Shopify topic', topic)
      const notificationService = new ShopifyNotificationService()
      const shopifyService = new ShopifyService()
      const payload = request.all()
      switch (topic) {
        case 'orders/cancelled':
          await notificationService.orderNotification({
            shopifyOrderId: payload.admin_graphql_api_id,
            customer: payload?.customer,
            orderName: payload.name,
            title: `Order cancellation`,
            description: `Your order ${payload.name} has been cancelled.`,
            status: topic,
          })
          // await this.syncOrderService.syncOrderFromShopifyWebhook(payload)
          await queue.dispatch(SyncShopifyWebhookOrderJob, { order: payload })
          break

        case 'orders/updated':
          if (payload?.financial_status === 'voided') {
            //Send email to shop if order payment is voided
            await mail.sendLater(new SaleOrderVoidedNotification('<EMAIL>', payload))
          }

          // await this.syncOrderService.syncOrderFromShopifyWebhook(payload)
          await queue.dispatch(SyncShopifyWebhookOrderJob, { order: payload })
          break

        case 'orders/fulfilled':
        case 'orders/paid':
        case 'orders/partially_fulfilled':
          // let title = 'Order is ready'
          // let description = `Your order ${payload.name} is getting ready! The shipping label has been printed and your package will soon be on its way.`
          // await notificationService.orderNotification({
          //   resourceId: payload.admin_graphql_api_id,
          //   customer: payload?.customer,
          //   orderId: payload.name,
          //   title: title,
          //   description: description,
          //   status: topic,
          // })

          // await this.syncOrderService.syncOrderFromShopifyWebhook(payload)
          await queue.dispatch(SyncShopifyWebhookOrderJob, { order: payload })
          break

        case 'orders/create':
          await notificationService.orderNotification({
            shopifyOrderId: payload.admin_graphql_api_id,
            customer: payload?.customer,
            orderName: payload.name,
            title: `Order confirmation`,
            description: `Thank you for your order! Your order ${payload.name} has been successfully placed.`,
            status: topic,
          })

          // delete draft order
          const draftOrderId = payload?.note_attributes?.find(
            (item: any) => item.name === 'draftOrderId'
          )?.value

          if (draftOrderId) {
            await queue.dispatch(DeleteDraftOrderByIdJob, {
              draftOrderId,
            })
          }

          // await this.syncOrderService.syncOrderFromShopifyWebhook(payload)
          await queue.dispatch(SyncShopifyWebhookOrderJob, { order: payload })

          await this.orderService.checkOrderOutOfStock(payload)

          // Update coupon on order created
          await this.orderService.updateCouponOnOrderCreated(payload)

          break

        case 'fulfillments/create':
          // console.log('fulfillments/create payload', payload)
          await this.orderService.createOrderFulfillment(payload, topic);
          break;

        case 'fulfillments/update':
          // console.log('fulfillments/update payload', payload);
          await this.orderService.updateOrderFulfillment(payload, topic);
          break;

        case 'fulfillment_orders/rescheduled':
          const date = DateTime.fromISO(payload.fulfillment_order.fulfill_at).toFormat(
            'LLL dd yyyy'
          )
          const fulfillmentOrderId = payload.fulfillment_order.id
          if (fulfillmentOrderId) {
            const shopifyOrder = await shopifyService.getOrderByFulfillOrderId(fulfillmentOrderId)
            if (shopifyOrder) {
              await notificationService.orderNotification({
                shopifyOrderId: shopifyOrder.admin_graphql_api_id,
                customer: shopifyOrder?.customer,
                orderName: shopifyOrder.name,
                title: `Delivery Rescheduled`,
                description: `Your delivery for order ${shopifyOrder.name} has been rescheduled for ${date}`,
                status: topic,
              })
            }
          }
          break

        case 'inventory_levels/update':
          if (payload?.admin_graphql_api_id && payload?.available) {
            await notificationService.restockNotification({
              inventoryLevelId: payload.admin_graphql_api_id,
              available: payload.available,
            })
          }
          break

        case 'products/create':
        case 'products/update':
          await queue.dispatch(
            WebhookShopifyProductsJob,
            { product: payload },
            { queueName: 'syncInventory' }
          )

          await queue.dispatch(
            ProductUpdateNotificationJob,
            { product: payload },
            {
              queueName: 'syncInventory',
            }
          )
          break

        case 'draft_orders/create':
          await this.orderService.checkOrderOutOfStock(payload, true)
          break
      }

      return response.ok('Works')
    } else {
      return response.badRequest('Works')
    }
  }

  async fulfil({ request, response }: HttpContext) {
    // @ts-ignore
    const payload = request.all()
    // @ts-ignore
    const products = Object.values(payload)
    await queue.dispatch(
      UpdateInventoryJob,
      { products },
      {
        queueName: 'webhook',
      }
    )

    return response.ok('Works')
  }

  async updateStream({ request, response }: HttpContext) {
    // @ts-ignore
    const payload = request.all()

    await queue.dispatch(StreamEndBroadcastJob, { stream: payload }, { queueName: 'liveStream' })

    return response.ok('Works')
  }
}

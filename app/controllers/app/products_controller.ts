import SyncBundleProductJob from '#jobs/sync_bundle_product_job'
import ZnBundleProduct from '#models/zn_bundle_product'
import ZnBundleProductItem from '#models/zn_bundle_product_item'
import ZnProduct, { EProductApproval } from '#models/zn_product'
import ZnProductCategory from '#models/zn_product_category'
import ZnProductReview from '#models/zn_product_review'
import ZnProductTag from '#models/zn_product_tag'
import ZnProductType from '#models/zn_product_type'
import ZnProductVariant from '#models/zn_product_variant'
import ZnRestockNotification from '#models/zn_restock_notification'
import { AffiliationService } from '#services/affiliation/affiliation_service'
import { ProductReviewService } from '#services/product_review_service'
import { ProductService } from '#services/shop/product_service'
import { FastBundleService } from '#services/shopify/fast_bundle_service'
import {
  createProductValidator,
  productShowValidator,
  restockNotificationValidator,
} from '#validators/app/product/product_validator'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'

export default class ProductsController {
  private productService
  private productReviewService
  private affiliationService

  constructor() {
    this.productService = new ProductService()
    this.productReviewService = new ProductReviewService()
    this.affiliationService = new AffiliationService()
  }
  /**
   * @restockNotification
   * @tag Products
   */
  async restockNotification({ request, response, user }: HttpContext) {
    const { shopifyVariantId, active } = await request.validateUsing(restockNotificationValidator)
    const variant = await ZnProductVariant.findBy('shopifyVariantId', shopifyVariantId)

    if (variant) {
      const data = {
        variantId: variant.id,
        userId: user!.id,
      }
      if (active) {
        await ZnRestockNotification.firstOrCreate(data)
      } else {
        await ZnRestockNotification.query().where(data).delete()
      }
    }

    return response.ok('Successfully')
  }

  /**
   * @show
   * @tag Products
   */
  async show({ request, response, user }: HttpContext) {
    const { shopifyProductId } = await request.validateUsing(productShowValidator)
    const product = await ZnProduct.query()
      .where('shopifyProductId', shopifyProductId)
      .whereNot('status', 'draft')
      .where('isGift', false)
      .preload('variants')
      .preload('images')
      .preload('options')
      .select('id')
      .first()

    if (!product) {
      return response.ok([])
    }

    const variantIds = product.variants.map((v) => v.id)
    const items = await ZnRestockNotification.query()
      .where({ userId: user!.id })
      .whereIn('variantId', variantIds)
      .preload('variant')

    return response.ok(items.map((item) => item.variant.shopifyVariantId))
  }

  /**
   * @detail
   * @tag Products
   * @summary Get product detail
   * @paramPath id - UUID or ShopifyId (e.g. 7551053660214) of Product - @example(03bed7a8-c37b-415f-956b-02af1d31d3a0)
   * @responseBody 200 - <ZnProduct>.with(variants,reviews,images,options,options.variantOptionValues,options.productOptionValues) - Get product detail descriptively
   */
  async detail({ response, params, request }: HttpContext) {
    try {
      const { isOriginalProduct = false } = request.qs()
      const productService = new ProductService()
      let product = await productService.getProductById(params.id)

      // Get main product when product is fast bundle product
      if (product && !isOriginalProduct) {
        const { mainProductId } = await productService.getMainProductId(product)
        // fetch main product
        if (mainProductId !== product.id) {
          product = await productService.getProductById(mainProductId)
        }
      }

      if (!product) {
        return response.notFound('Product not found')
      }

      if (isOriginalProduct) {
        await product.load('mainBundleProduct', (query) => {
          query
            .preload('items', (query) => {
              query
                .where('isActive', true)
                .preload('product')
                .preload('variants', (query) => {
                  query.preload('image')
                })
            })
            .preload('collections', (query) => {
              query.preload('items', (query) => {
                query.preload('variants', (query) => {
                  query.preload('image')
                })
              })
            })
            .preload('discounts')
            .preload('discount')
            .preload('mainProduct', (query) => {
              query.preload('images').preload('variants')
            })
        })
      }

      const reviewSummary = await this.productReviewService.reviewSummary(product.id)

      return response.ok({ ...product.toJSON(), reviewSummary })
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  async metaDataDetail({ response, params }: HttpContext) {
    try {
      const product = await ZnProduct.query()
        .where((queryBuilder) => {
          queryBuilder
            .where('id', params.id)
            .orWhere('shopifyProductId', `gid://shopify/Product/${params.id}`)
        })
        .whereNot('status', 'draft')
        .where('isGift', false)
        .preload('image')
        .select('id', 'title', 'shopifyProductId')
        .first()

      if (!product) {
        return response.notFound('Product not found')
      }

      return response.ok(product)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @list
   * @tag Products
   * @summary List Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery shopifyCollectionId - Shopify Collection Id - @type(string)
   * @paramQuery collectionId - Collection Id - @type(string)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery sortBy - Search Term - @enum(a2z,z2a,lowest,highest,oldest,newest)
   * @paramQuery status - Product Status - @enum(draft,active,archived)
   * @responseBody 200 - <ZnProduct[]>.with(variants,reviews,images,options,options.variantOptionValues,options.productOptionValues) - Get products list descriptively
   */
  async list({ request, response }: HttpContext) {
    const {
      page = 1,
      pageSize = 10,
      shopifyCollectionId,
      search,
      collectionId,
      sortBy,
      status,
      vendorId,
      categoryId,
      typeId,
      tagIds,
    } = request.qs()

    const queryProducts = ZnProduct.query()
      // .whereNot('status', 'draft')
      .where('isGift', false)
      .preload('variant')
      .preload('reviewsSummary')
      .has('variant')
      .preload('image')
      .preload('category')
      .preload('vendor')
      .preload('productType')
      .preload('variants', (variantQuery) => {
        variantQuery.preload('image')
      })

    if (shopifyCollectionId) {
      const productIds = await this.productService.productIdsByShopifyCollectionId([
        shopifyCollectionId,
      ])

      queryProducts.whereIn('id', productIds)
    }

    if (collectionId) {
      const productIds = await this.productService.productIdsByCollectionId(collectionId)
      queryProducts.whereIn('id', productIds)
    }

    if (search) {
      const searchTitle = search.replaceAll(' ', '%')
      const productIds = (
        await ZnProductVariant.query()
          .whereILike('title', `%${searchTitle}%`)
          .orWhereILike('barcode', `%${search}%`)
          .orWhereILike('sku', `%${search}%`)
          .select('productId')
          .groupBy('productId')
          .limit(50)
      ).map((v) => v.productId)

      queryProducts.where((query) => {
        query.whereIn('id', productIds).orWhereILike('title', `%${searchTitle}%`)
      })
    }

    if (vendorId) {
      queryProducts.whereHas('vendor', (vendorQuery) => {
        vendorQuery.where({ id: vendorId })
      })
    }

    if (categoryId) {
      queryProducts.whereHas('category', (categoryQuery) => {
        categoryQuery.where({ id: categoryId })
      })
    }

    if (typeId) {
      queryProducts.whereHas('productType', (typeQuery) => {
        typeQuery.where({ id: typeId })
      })
    }

    if (tagIds && tagIds.length > 0) {
      queryProducts.whereHas('tags', (tagQuery) => {
        tagQuery.whereIn('id', tagIds)
      })
    }

    if (sortBy) {
      switch (sortBy) {
        case 'a2z':
          queryProducts.orderBy('title', 'asc')
          break
        case 'z2a':
          queryProducts.orderBy('title', 'desc')
          break
        case 'lowest': {
          queryProducts.orderBy('price', 'asc')
          break
        }
        case 'highest': {
          queryProducts.orderBy('price', 'desc')
          break
        }
        case 'oldest':
          queryProducts.orderBy('createdAt', 'asc')
          break
        case 'newest':
          queryProducts.orderBy('createdAt', 'desc')
          break
        default:
      }
    }

    if (status) {
      queryProducts.where({ status })
    } else {
      queryProducts.whereNot({ status: 'draft' })
    }

    const products = await queryProducts.paginate(page, pageSize)

    return response.ok(products)
  }

  /**
   * @relate
   * @tag Products
   * @summary Get related products
   * @paramPath id - UUID or ShopifyId (e.g. 7551053660214) of Product - @example(03bed7a8-c37b-415f-956b-02af1d31d3a0)
   * @paramQuery page - Page number (default 1) - @type(number)
   * @paramQuery pageSize - Page number (default 10) - @type(number)
   * @responseBody 200 - <ZnProduct[]>.with(variant,image).append("reviewSummary":{"latestReviews":["ZnProductReview"],"totalReviews":3,"averageRating":3.5,"details":{"1":0,"2":0,"3":1,"4":0,"5":0}}).paginated() - Get related productsdescriptively
   */
  async relate({ request, response, params }: HttpContext) {
    const { page = 1, pageSize = 10 } = request.qs()
    const product = await ZnProduct.query()
      .where((queryBuilder) => {
        queryBuilder
          .where('id', params.id)
          .orWhere('shopifyProductId', `gid://shopify/Product/${params.id}`)
      })
      .preload('collections')
      .firstOrFail()

    const productIds = await this.productService.productIdsByShopifyCollectionId(
      product.collections.map((c) => c.shopifyCollectionId).filter((v) => !!v) as string[]
    )

    const queryProducts = this.productService.getProductsByIds(productIds)
    const products = await queryProducts.paginate(page, pageSize)
    return response.ok(products)
  }

  /**
   * @reviews
   * @tag Products
   * @summary Get product reviews
   * @paramPath id - UUID or ShopifyId (e.g. 7551053660214) of Product - @example(03bed7a8-c37b-415f-956b-02af1d31d3a0)
   * @paramQuery page - Page number (default 1) - @type(number)
   * @paramQuery pageSize - Page number (default 10) - @type(number)
   * @paramQuery rating - Rating - @type(number)
   * @responseBody 200 - <ZnProductReview[]>.with(user).paginated() - Get product reviews descriptively
   */
  async reviews({ request, response, params }: HttpContext) {
    const { page = 1, pageSize = 10, rating } = request.qs()
    const product = await ZnProduct.query()
      .where('id', params.id)
      .orWhere('shopifyProductId', `gid://shopify/Product/${params.id}`)
      .first()

    if (!product) {
      return response.notFound('Product not found')
    }

    const queryReviews = ZnProductReview.query().where({ status: 1, productId: product.id })
    if (rating) {
      queryReviews.where('rating', rating)
    }

    const reviews = await queryReviews
      .preload('user')
      .orderBy('createdAt', 'desc')
      .paginate(page, pageSize)

    return response.ok(reviews)
  }

  public async bundles({ response, request }: HttpContext) {
    const { shopifyProductId, productId } = request.qs()
    const bundleService = new FastBundleService()
    const productService = new ProductService()
    let product = null
    if (shopifyProductId) {
      product = await ZnProduct.findBy('shopifyProductId', shopifyProductId)
    }

    if (productId) {
      product = await ZnProduct.find(productId)
    }
    let bundleId = null
    if (product) {
      await product.load('vendor')
      const { mainProductId, ...result } = await productService.getMainProductId(product)
      bundleId = result.bundleId

      // fetch main product
      if (mainProductId !== product.id) {
        product = await ZnProduct.query()
          .where('id', mainProductId)
          .orWhere('shopifyProductId', `gid://shopify/Product/${mainProductId}`)
          .first()
      }
    }

    if (!product) {
      return response.notFound('Product not found')
    }

    const bundles = await bundleService.getBundleByProductId(product)

    await queue.dispatch(
      SyncBundleProductJob,
      { bundles },
      {
        queueName: 'syncData',
      }
    )

    return response.ok(bundleId ? bundles.filter((b: any) => b.id === bundleId) : bundles)
  }

  public async listBundles({ response, request }: HttpContext) {
    const { shopifyProductId, productId } = request.qs()
    let product = null
    if (shopifyProductId) {
      product = await ZnProduct.findBy('shopifyProductId', shopifyProductId)
    }

    if (productId) {
      product = await ZnProduct.find(productId)
    }
    if (!product) {
      return response.notFound('Product not found')
    }

    const bundleItems = await ZnBundleProductItem.query()
      .where('productId', product.id)
      .where('showInPage', true)
      .select('bundleProductId')
    const bundleCollections = await db
      .from('zn_bundle_collection_items')
      .leftJoin(
        'zn_bundle_collections',
        'zn_bundle_collections.id',
        'zn_bundle_collection_items.bundleCollectionId'
      )
      .where('zn_bundle_collection_items.productId', product.id)
      .where('zn_bundle_collections.showInPage', true)
      .whereNull('zn_bundle_collection_items.deletedAt')
      .whereNull('zn_bundle_collections.deletedAt')
      .select(db.raw('zn_bundle_collection_items.bundleId as bundleId'))

    const bundleIds = Array.from(
      new Set([
        ...bundleItems.map((item) => item.bundleProductId),
        ...bundleCollections.map((item) => item.bundleId),
      ])
    )
    const otherBundles = await ZnBundleProduct.query()
      .whereIn('id', bundleIds)
      .where('isActive', true)
      .whereNot('type', 'add_on')
      .preload('items', (query) => {
        query
          .where('isActive', true)
          .preload('product', (query) => {
            query.preload('image')
          })
          .preload('variants', (query) => {
            query.preload('image')
          })
      })
      .preload('collections', (query) => {
        query.preload('items', (query) => {
          query.preload('variants', (query) => {
            query.preload('image')
          })
        })
      })
      .preload('discounts')
      .preload('discount')
      .preload('mainProduct', (query) => {
        query.preload('images').preload('variants')
      })

    const addOnBundle = await ZnBundleProduct.query()
      .whereIn('id', bundleIds)
      .where('isActive', true)
      .where('type', 'add_on')
      .preload('items', (query) => {
        query
          .where('isActive', true)
          .preload('product')
          .preload('variants', (query) => {
            query.preload('image')
          })
      })
      .preload('discounts')
      .preload('discount')
      .preload('mainProduct', (query) => {
        query.preload('images').preload('variants')
      })
      .first()

    return response.ok({
      addOnBundle,
      otherBundles,
    })
  }

  public async bundleDetail({ response, params }: HttpContext) {
    const { id } = params

    const bundle = await ZnBundleProduct.query()
      .where((queryBuilder) => {
        queryBuilder.where('fastBundleUuid', id).orWhere('id', id).orWhere('fastBundleId', id)
      })
      .where('isActive', true)
      .preload('items', (query) => {
        query
          .where('isActive', true)
          .preload('product')
          .preload('variants', (query) => {
            query.preload('image')
          })
      })
      .preload('collections', (query) => {
        query.preload('items', (query) => {
          query.preload('variants', (query) => {
            query.preload('image')
          })
        })
      })
      .preload('discounts')
      .preload('discount')
      .preload('mainProduct', (query) => {
        query.preload('images').preload('variants')
      })
      .first()

    if (!bundle) {
      return response.notFound('Bundle not found')
    }

    return response.ok(bundle)
  }

  public async getAffiliateInfo({ user, params, response }: HttpContext) {
    try {
      if (!user || user === undefined) {
        return response.ok({
          commissionPercent: 0,
          discountPercent: 0,
        })
      }

      const userId = user!.id
      const productId = params.id

      const { commissionPercent, discountPercent } =
        await this.affiliationService.getCommissionAndDiscountPercentage({
          userId,
          productId,
        })

      return response.ok({
        commissionPercent: commissionPercent,
        discountPercent: discountPercent,
      })
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @showVideos
   * @tag Products
   * @summary Get videos relate to product
   * @paramPath id - UUID of Product
   * @responseBody 200 - List video relate to product
   */
  async showVideos({ response, params }: HttpContext) {
    try {
      const productId = params.id

      const product = await ZnProduct.find(productId)

      if (!product) {
        return response.notFound('Product not found')
      }

      const videos = await this.productService.getVideosByProductId(productId)

      return response.ok(
        videos.map(({ timeline, post, stream }) => ({
          timeline: timeline
            ? {
                id: timeline.id,
                start: timeline.start,
                end: timeline.end,
                variantId: timeline.variantId,
                variant: timeline.variant?.serialize(),
              }
            : null,
          post: post.serialize(),
          stream: stream
            ? typeof stream.serialize === 'function'
              ? stream.serialize()
              : stream
            : null,
        }))
      )
    } catch (error) {
      return response.internalServerError({
        message: 'Error fetching videos',
        error: error.message,
      })
    }
  }



  /**
   * @listVendors
   * @tag Products
   * @summary List Vendors
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @responseBody 200 - <ZnVendor[]> - OK
   */
  async listVendors({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search
      } = request.qs();

      const vendors = await this.productService.listVendors({ page, pageSize, search })

      return response.ok(vendors);

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @listProductCategories
   * @tag Products
   * @summary List Product Categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @responseBody 200 - <ZnProductCategory[]> - OK
   */
  async listProductCategories({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
      } = request.qs();

      const query = ZnProductCategory.query()
        .orderBy('name', 'asc')

      if (search) {
        query.whereRaw(`LOWER(name) LIKE LOWER(?)`, [`%${search}%`])
      }

      const result = await query.paginate(page, pageSize)

      return response.ok(result);

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @listProductTypes
   * @tag Products
   * @summary List Product Types
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @responseBody 200 - <ZnProductType[]> - OK
   */
  async listProductTypes({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
      } = request.qs();

      const query = ZnProductType.query()
        .orderBy('name', 'asc')

      if (search) {
        query.whereRaw(`LOWER(name) LIKE LOWER(?)`, [`%${search}%`])
      }

      const result = await query.paginate(page, pageSize)

      return response.ok(result);

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @listProductTags
   * @tag Products
   * @summary List Product Tags
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery pageSize - Page Size (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @responseBody 200 - <ZnProductTag[]> - OK
   */
  async listProductTags({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        filter,
      } = request.qs();

      const query = ZnProductTag.query()
        .orderBy('name', 'asc')

      if (search) {
        query.whereRaw(`LOWER(name) LIKE LOWER(?)`, [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, pageSize)

      return response.ok(result);

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @createProduct
   * @tag Products
   * @summary Create Product
   * @requestBody <ZnProduct>
   * @responseBody 200 - <ZnProduct> - OK
   */
  async createProduct({ request, response }: HttpContext) {
    const data = request.all();

    const payload = await createProductValidator.validate(data)

    try {
      let product
      // if (payload.status == 'draft') {
      //   product = await this.productService.createProduct(payload)
      // } else {
      //   payload.status = 'draft'
      //   product = await this.productService.createProduct(payload)

      //   await product.merge({ pendingApproval: EProductApproval.ACTIVATE }).save()
      // }

      product = await this.productService.createProduct(payload)

      response.ok(product)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @updateProduct
   * @tag Products
   * @summary Update Product
   * @paramPath id - Id of Product - @type(string) @required
   * @requestBody <ZnProduct>
   * @responseBody 200 - <ZnProduct> - OK
   */
  async updateProduct({ params, request, response }: HttpContext) {
    const id = params.id

    const dbProduct = await ZnProduct.find(id)

    if (!dbProduct) { return response.notFound("Product Not Found") }

    const data = request.all();

    const payload = await createProductValidator.validate(data)

    try {
      let product
      if (payload.status == 'draft') {
        product = await this.productService.updateProduct(dbProduct.shopifyProductId, payload)
      } else {
        dbProduct.pendingChanges = JSON.stringify(data)
        if (data.requiresApproval) {
          if (dbProduct.pendingApproval != EProductApproval.ACTIVATE) {
            dbProduct.pendingApproval = EProductApproval.CHANGE
          }
        }
        await dbProduct.save()
      }

      response.ok(product)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }

  /**
   * @deleteProduct
   * @tag Products
   * @summary Delete Product
   * @paramPath id - Id of Product - @type(string) @required
   * @responseBody 200 - <ZnProduct> - OK
   */
  async deleteProduct({ params, response }: HttpContext) {
    try {
      const id = params.id
      const product = await ZnProduct.find(id)
      if (!product) { return response.notFound("Product Not Found") }

      if (product.status == 'draft') {
        await this.productService.deleteProduct(product.id)
      } else {
        product.pendingApproval = EProductApproval.DELETE
        await product.save()
      }

      response.ok(product)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      })
    }
  }
}

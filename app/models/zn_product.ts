import AppModel from '#models/app_model'
import ZnCollection from '#models/zn_collection'
import ZnProductCategory from '#models/zn_product_category'
import ZnProductImage from '#models/zn_product_image'
import ZnProductOption from '#models/zn_product_option'
import ZnProductReview from '#models/zn_product_review'
import ZnProductVariant from '#models/zn_product_variant'
import {
  afterFind,
  belongsTo,
  column,
  computed,
  hasMany,
  hasOne,
  manyToMany,
} from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import { formatNumber } from '../../services/commons.js'
import ZnBundleProduct from './zn_bundle_product.js'
import ZnChannel from './zn_channel.js'
import ZnProductTag from './zn_product_tag.js'
import ZnProductType from './zn_product_type.js'
import ZnVendor from './zn_vendor.js'

const ADS_WEBSITE_DOMAIN = process.env.ADS_WEBSITE_DOMAIN || ''

export enum EProductApproval {
  ACTIVATE = 1,
  CHANGE = 2,
  DELETE = 3,
}

export default class ZnProduct extends AppModel {
  @column({ columnName: 'shopifyProductId' })
  declare shopifyProductId: string

  @column({ columnName: 'fulfilProductId' })
  public declare fulfilProductId: string | null

  @column()
  declare title: string

  @column()
  public declare description: string | null

  @column({ columnName: 'vendorId' })
  declare vendorId: string | null

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId',
  })
  declare vendor: BelongsTo<typeof ZnVendor>

  @column({ columnName: 'productTypeId' })
  declare productTypeId: string | null

  @belongsTo(() => ZnProductType, {
    foreignKey: 'productTypeId',
  })
  declare productType: BelongsTo<typeof ZnProductType>

  @column()
  declare handle: string

  @column({ columnName: 'isGift' })
  declare isGift: boolean

  @column()
  declare price: number

  @column({ columnName: 'compareAtPrice' })
  declare compareAtPrice: number | null

  @column()
  // @no-swagger
  declare status: 'active' | 'archived' | 'draft'

  // @column()
  // declare public tags: string | null

  @manyToMany(() => ZnProductTag, {
    pivotTable: 'zn_products_product_tags',
    pivotForeignKey: 'productId',
    pivotRelatedForeignKey: 'productTagId',
  })
  declare tags: ManyToMany<typeof ZnProductTag>

  @column.dateTime({ columnName: 'publishedAt' })
  declare publishedAt: DateTime | null

  @hasOne(() => ZnProductVariant, {
    foreignKey: 'productId',
    onQuery(query) {
      query.orderBy('position', 'asc')
    },
  })
  declare variant: HasOne<typeof ZnProductVariant>

  @hasMany(() => ZnProductVariant, {
    foreignKey: 'productId',
  })
  declare variants: HasMany<typeof ZnProductVariant>

  @hasMany(() => ZnProductImage, {
    foreignKey: 'productId',
    onQuery(query) {
      query.orderBy('position', 'asc')
    },
  })
  declare images: HasMany<typeof ZnProductImage>

  @hasOne(() => ZnProductImage, {
    foreignKey: 'productId',
  })
  declare image: HasOne<typeof ZnProductImage>

  @hasMany(() => ZnProductOption, {
    foreignKey: 'productId',
    onQuery(query) {
      query.orderBy('shopifyOptionId')
    },
  })
  declare options: HasMany<typeof ZnProductOption>

  @manyToMany(() => ZnCollection, {
    pivotTable: 'zn_product_collections',
    pivotForeignKey: 'productId',
    pivotRelatedForeignKey: 'collectionId',
  })
  declare collections: ManyToMany<typeof ZnCollection>

  @manyToMany(() => ZnChannel, {
    pivotTable: 'zn_products_channels',
    pivotForeignKey: 'productId',
    pivotRelatedForeignKey: 'channelId',
  })
  public declare channels: ManyToMany<typeof ZnChannel>

  /**
   * Relationship: Link product to its reviews.
   */
  @hasMany(() => ZnProductReview, {
    foreignKey: 'productId',
    onQuery(query) {
      query.where({
        status: 1,
      })
    },
  })
  // @no-swagger
  declare reviews: HasMany<typeof ZnProductReview>

  // Internally used to compute reviewSummary
  @hasMany(() => ZnProductReview, {
    foreignKey: 'productId',
    serializeAs: null,
    onQuery(query) {
      query.where('status', 1).orderBy('createdAt', 'desc')
    },
  })
  // @no-swagger
  declare reviewsSummary: HasMany<typeof ZnProductReview>

  @column({ columnName: 'categoryId' })
  declare categoryId: string | null

  @belongsTo(() => ZnProductCategory, {
    foreignKey: 'categoryId',
  })
  declare category: BelongsTo<typeof ZnProductCategory>

  @column({ columnName: 'pickupOnly' })
  public declare pickupOnly: boolean

  @column({ columnName: 'classification' })
  // @no-swagger
  public declare classification: 'material' | 'product' | null

  @hasOne(() => ZnBundleProduct, {
    foreignKey: 'mainProductId',
  })
  declare mainBundleProduct: HasOne<typeof ZnBundleProduct>

  @column({ columnName: 'pendingChanges' })
  declare pendingChanges: any

  @column({ columnName: 'pendingApproval' })
  declare pendingApproval: EProductApproval | null

  /**
   * Computed property: Get the product summary from its reviews.
   */
  @computed()
  // @no-swagger
  get reviewSummary() {
    let latestReviews: ZnProductReview[] = []
    let totalReviews = 0
    let averageRating = 0
    const details = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0,
    } as any

    const reviews = this.reviewsSummary

    if (reviews && reviews.length > 0) {
      latestReviews = reviews
        .sort((a, b) => {
          if (a.createdAt < b.createdAt) {
            return 1
          } else if (a.createdAt > b.createdAt) {
            return -1
          } else {
            return 0
          }
        })
        .slice(0, 3)
      totalReviews = reviews.length
      averageRating = formatNumber(
        reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews
      )

      for (let star = 1; star <= 5; star++) {
        details[star] = reviews.reduce((sum, review) => {
          if (review.rating === star) {
            return sum + 1
          }

          return sum
        }, 0)
      }
    }

    return {
      latestReviews,
      totalReviews,
      averageRating,
      details,
    }
  }

  @computed()
  get onlineStoreUrl() {
    return [ADS_WEBSITE_DOMAIN, 'product', this.id].join('/')
  }

  serializeExtras() {
    return {
      variantsCount: this.$extras.variants_count,
      compareAtPrice: this.$extras.compareAtPrice,
      pivotOrderBy: this.$extras.pivot_orderBy,
    }
  }

  @afterFind()
  public static async fillPrice(product: ZnProduct) {
    if (!product?.id) return

    const variant = await ZnProductVariant.query()
      .where('productId', product.id)
      .whereNull('deletedAt')
      .orderBy([
        {
          column: 'inventoryQuantity',
          order: 'desc',
        },
        {
          column: 'price',
          order: 'asc',
        },
        {
          column: 'position',
          order: 'asc',
        },
      ])
      .first()

    if (variant) {
      product.price = variant.price || 0
      product.$extras.compareAtPrice = variant.compareAtPrice || null
    } else {
      product.price = 0
      product.$extras.compareAtPrice = null
    }
  }
}

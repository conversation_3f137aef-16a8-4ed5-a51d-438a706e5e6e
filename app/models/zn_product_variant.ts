import AppModel from '#models/app_model'
import ZnProduct from '#models/zn_product'
import { afterFetch, belongsTo, column, manyToMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, ManyToMany } from '@adonisjs/lucid/types/relations'
import ZnProductImage from './zn_product_image.js'
import ZnProductOptionValue from './zn_product_option_value.js'
import ZnSupplier from './zn_supplier.js'
import ZnUser from './zn_user.js'

export default class ZnProductVariant extends AppModel {
  @column({ columnName: 'shopifyVariantId' })
  declare shopifyVariantId: string

  @column({ columnName: 'fulfilVariantId' })
  declare public fulfilVariantId: string | null

  @column({ columnName: 'productId' })
  declare productId: string

  @column({ columnName: 'legacyResourceId' })
  declare legacyResourceId: string

  @column({ columnName: 'imageId' })
  declare imageId: string | null

  @column()
  declare title: string | null

  @column()
  declare price: number

  @column({ columnName: 'compareAtPrice' })
  declare compareAtPrice: number | null

  @column()
  declare sku: string | null

  @column()
  declare position: number | null

  @column({ columnName: 'inventoryQuantity' })
  declare inventoryQuantity: number

  @column({ columnName: 'maxQuantity' })
  declare maxQuantity: number

  @column({ columnName: 'inventoryPolicy' })
  // @no-swagger
  declare inventoryPolicy: 'continue' | 'deny'

  @column({ columnName: 'inventoryManagement' })
  // @no-swagger
  declare inventoryManagement: 'shopify' | 'thirdParty' | 'none'

  @column()
  declare weight: number | null

  @column({ columnName: 'weightUnit' })
  // @no-swagger
  declare weightUnit: 'kg' | 'g' | 'lb' | 'oz' | null

  @column()
  declare barcode: string | null

  @column({ columnName: 'availableForSale' })
  declare availableForSale: boolean

  @belongsTo(() => ZnProduct, {
    foreignKey: 'productId',
  })
  declare product: BelongsTo<typeof ZnProduct>

  @belongsTo(() => ZnProductImage, {
    foreignKey: 'imageId',
  })
  declare public image: BelongsTo<typeof ZnProductImage>

  @manyToMany(() => ZnUser, {
    pivotTable: 'zn_restock_notifications',
    pivotForeignKey: 'variantId',
    pivotRelatedForeignKey: 'userId',
  })
  declare restockNotifyUsers: ManyToMany<typeof ZnUser>

  // @hasMany(() => ZnVariantOptionValue, {
  //   foreignKey: 'variantId',
  // })
  // // @no-swagger
  // declare optionValues: HasMany<typeof ZnVariantOptionValue>
  
  @manyToMany(() => ZnProductOptionValue, {
    pivotTable: 'zn_variants_option_values',
    pivotForeignKey: 'variantId',
    pivotRelatedForeignKey: 'optionValueId',
  })
  declare optionValues: ManyToMany<typeof ZnProductOptionValue>

  @column({ columnName: 'supplierId' })
  declare supplierId: string | null
  @belongsTo(() => ZnSupplier, {
    foreignKey: 'supplierId',
  })
  declare public supplier: BelongsTo<typeof ZnSupplier>

  // add products' images to variants if variants don't have them
  @afterFetch()
  static async addProductsImages(variants: ZnProductVariant[]) {
    for (const variant of variants) {
      if (variant.$preloaded.image === null) {

        const product = await ZnProduct.query()
          .where('id', variant.productId)
          .preload('image')
          .first()

        if (product?.image) {
          variant.$preloaded.image = product?.image
        }
      }
    }
  }
}

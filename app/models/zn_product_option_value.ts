import { belongsTo, column, manyToMany } from '@adonisjs/lucid/orm'
import AppModel from '#models/app_model'
import ZnProductOption from './zn_product_option.js'
import type { BelongsTo, ManyToMany } from '@adonisjs/lucid/types/relations'
import ZnProductVariant from './zn_product_variant.js'

export default class ZnProductOptionValue extends AppModel {
  @column({ columnName: 'optionId' })
  declare public optionId: string

  @belongsTo(() => ZnProductOption, {
    foreignKey: 'optionId',
  })
  declare public option: BelongsTo<typeof ZnProductOption>

  @manyToMany(() => ZnProductVariant, {
    pivotTable: 'zn_variants_option_values',
    pivotForeignKey: 'optionValueId',
    pivotRelatedForeignKey: 'variantId',
  })
  declare variants: ManyToMany<typeof ZnProductVariant>

  @column({ columnName: 'shopifyOptionValueId' })
  declare public shopifyOptionValueId: string

  @column()
  declare public value: string

  @column()
  declare public position: number | null
}

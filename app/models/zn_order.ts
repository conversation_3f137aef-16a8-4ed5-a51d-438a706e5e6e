import AppModel from '#models/app_model'
import ZnAddress from '#models/zn_address'
import ZnOrderDetail from '#models/zn_order_detail'
import ZnUser from '#models/zn_user'
import { belongsTo, column, computed, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import { DateTime } from 'luxon'
import ZnAffiliateCommission from './zn_affiliate_commission.js'
import ZnOrderDiscount from './zn_order_discount.js'
import ZnOrderFulfillment from './zn_order_fulfillment.js'

export default class ZnOrder extends AppModel {
  @column({ columnName: 'shopifyId' })
  declare shopifyId: string

  @column()
  declare name: string

  @column()
  declare email: string

  @column()
  declare status: string

  @column({ columnName: 'financialStatus' })
  declare financialStatus: string

  @column({
    columnName: 'fulfillmentStatus',
    serialize: (value) => value || 'unfulfilled',
  })
  declare fulfillmentStatus: string

  @column.dateTime({ columnName: 'fulfilledAt' })
  declare fulfilledAt: DateTime | null

  @column.dateTime({ columnName: 'cancelledAt' })
  declare cancelledAt: DateTime | null

  @column.dateTime({ columnName: 'closedAt' })
  declare closedAt: DateTime | null

  @column({
    columnName: 'totalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare totalPrice: number

  @column({
    columnName: 'currentTotalPrice',
    consume: (value: string) => parseFloat(value),
  })
  declare currentTotalPrice: number

  @column()
  declare currency: string

  @column({ columnName: 'customerId' })
  declare customerId: string

  @column({ columnName: 'userId' })
  declare userId: string | null

  @column({ columnName: 'subtotalPrice' })
  declare subtotalPrice: number

  @column({ columnName: 'totalTax' })
  declare totalTax: number

  @column({ columnName: 'totalDiscounts' })
  declare totalDiscounts: number

  @column({ columnName: 'totalShipping' })
  declare totalShipping: number

  @column({ columnName: 'note' })
  declare note: string | null

  @belongsTo(() => ZnUser, {
    foreignKey: 'userId',
  })
  declare user: BelongsTo<typeof ZnUser>

  @column({ columnName: 'billingId' })
  declare billingId: string

  @belongsTo(() => ZnAddress, {
    foreignKey: 'billingId',
  })
  declare billing: BelongsTo<typeof ZnAddress>

  @belongsTo(() => ZnAddress, {
    foreignKey: 'billingId',
  })
  public declare billingAddress: BelongsTo<typeof ZnAddress>

  @column({ columnName: 'shippingId' })
  declare shippingId: string

  @belongsTo(() => ZnAddress, {
    foreignKey: 'shippingId',
  })
  declare shipping: BelongsTo<typeof ZnAddress>

  @belongsTo(() => ZnAddress, {
    foreignKey: 'shippingId',
  })
  public declare shippingAddress: BelongsTo<typeof ZnAddress>

  @hasMany(() => ZnOrderDetail, {
    foreignKey: 'orderId',
  })
  public declare orderDetails: HasMany<typeof ZnOrderDetail>

  @hasOne(() => ZnOrderDetail, {
    foreignKey: 'orderId',
  })
  public declare firstOrderDetail: HasOne<typeof ZnOrderDetail>

  @hasMany(() => ZnOrderDiscount, {
    foreignKey: 'orderId',
  })
  public declare orderDiscounts: HasMany<typeof ZnOrderDiscount>

  @hasOne(() => ZnAffiliateCommission, {
    foreignKey: 'orderId',
  })
  declare affliateCommmision: HasOne<typeof ZnAffiliateCommission>

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingNumber' })
  declare trackingNumber: string

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingCompany' })
  declare trackingCompany: string

  // @deprecated: Use 'fulfillments' instead
  @column({ columnName: 'trackingUrl' })
  declare trackingUrl: string

  @hasMany(() => ZnOrderFulfillment, {
    foreignKey: 'orderId'
  })
  declare fulfillments: HasMany<typeof ZnOrderFulfillment>;

  @computed()
  get zurnoStatus() {
    if (this.cancelledAt) {
      return 'cancelled'
    } else {
      if (this.closedAt) {
        return 'completed'
      }

      return 'ongoing'
    }
  }

  serializeExtras() {
    return {
      orderDetailsCount: this.$extras.orderDetails_count,
    }
  }
}

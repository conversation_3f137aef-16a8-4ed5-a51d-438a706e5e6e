import ZnBundleProduct from '#models/zn_bundle_product'
import ZnCartSection from '#models/zn_cart_section'
import ZnProduct from '#models/zn_product'
import ZnProductVariant from '#models/zn_product_variant'
import vine from '@vinejs/vine'

export const cartAddItemValidator = vine.compile(
  vine.object({
    variantId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProductVariant.table).where('id', field).first()
      }),
    quantity: vine.number(),
    cartId: vine.string().uuid().optional(),
  })
)

export const cartAddBundleValidator = vine.compile(
  vine.object({
    bundleId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnBundleProduct.table).where('id', field).first()
      }),
    quantity: vine.number(),
    cartId: vine.string().uuid().optional(),
    discountId: vine.string().uuid().optional(),
    items: vine
      .array(
        vine.object({
          variantId: vine
            .string()
            .uuid()
            .exists(async (query, field) => {
              return await query.from(ZnProductVariant.table).where('id', field).first()
            }),
          itemId: vine.string(),
        })
      )
      .optional(),
    collections: vine
      .array(
        vine.object({
          id: vine.string().uuid(),
          items: vine.array(
            vine.object({
              id: vine.string(),
              variants: vine.array(
                vine.object({
                  id: vine.string().uuid(),
                  quantity: vine.number(),
                })
              ),
            })
          ),
        })
      )
      .optional(),
  })
)

export const updateQuantityItemValidator = vine.compile(
  vine.object({
    quantity: vine.number(),
    cartSectionId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnCartSection.table).where('id', field).first()
      }),
  })
)

export const deleteCartSectionValidator = vine.compile(
  vine.object({
    cartSectionIds: vine.array(vine.string().uuid()),
    cartId: vine.string().uuid(),
  })
)

export const cartAddAllItemsValidator = vine.compile(
  vine.object({
    cartId: vine.string().uuid(),
    productId: vine
      .string()
      .uuid()
      .exists(async (query, field) => {
        return await query.from(ZnProduct.table).where('id', field).first()
      }),
    quantity: vine.number(),
  })
)

import vine from '@vinejs/vine'

export const restockNotificationValidator = vine.compile(
  vine.object({
    shopifyVariantId: vine.string(),
    active: vine.boolean(),
  })
)

export const productShowValidator = vine.compile(
  vine.object({
    shopifyProductId: vine.string(),
  })
)

export const createProductValidator = vine.compile(
  vine.object({
    title: vine.string(),
    description: vine.string().optional(),
    status: vine.enum(['draft', 'active']).optional(),

    productType: vine.object({
      name: vine.string()
    }).optional(),

    tags: vine.array(
      vine.object({
        name: vine.string()
      })
    ).optional(),

    category: vine.object({
      shopifyId: vine.string()
    }).optional(),

    medias: vine.array(
      vine.object({
        url: vine.string()
      })
    ).optional(),

    options: vine.array(
      vine.object({
        name: vine.string(),
        position: vine.number().optional(),
        productOptionValues: vine.array(
          vine.object({
            value: vine.string()
          })
        ).optional()
      })
    ).optional(),

    variants: vine.array(
      vine.object({
        optionValues: vine.array(
          vine.object({
            value: vine.string(),
            option: vine.object({
              name: vine.string(),
            })
          })
        ),

        sku: vine.string().optional(),
        barcode: vine.string().optional(),
        inventoryQuantity: vine.number().optional(),
        inventoryPolicy: vine.enum(['continue', 'deny']).optional(),

        price: vine.number().optional(),
        compareAtPrice: vine.number().optional(),

        weight: vine.number().optional(),
        weightUnit: vine.string().optional(),
      })
    ),
  })
)

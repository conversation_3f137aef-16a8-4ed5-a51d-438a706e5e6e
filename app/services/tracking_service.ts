import { TRACKING_ACTION } from '#constants/tracking'
import CreateTrackingJ<PERSON> from '#jobs/create_tracking_job'
import UpdateTracking<PERSON>napshotJob from '#jobs/update_tracking_snapshot_job'
import ZnPost from '#models/zn_post'
import ZnProduct from '#models/zn_product'
import ZnProductVariant from '#models/zn_product_variant'
import ZnResourceInteracts from '#models/zn_resource_interacts'
import ZnStore from '#models/zn_store'
import ZnStream from '#models/zn_stream'
import ZnTracking from '#models/zn_tracking'
import ZnUser from '#models/zn_user'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import db from '@adonisjs/lucid/services/db'
import queue from '@rlanz/bull-queue/services/main'
import * as firebase from 'firebase-admin'
import { AmazonS3StorageService } from '../../services/aws/s3/aws-s3.service.js'
import { FirebaseService } from '../../services/firebase/index.js'

interface TrackingServiceGetViewData {
  resourceId: string
  action: TRACKING_ACTION
}

interface TrackingServiceGetInteractionsData {
  resourceId: string
  actions: TRACKING_ACTION[]
}

const normalizeLocationString = (str: string = ''): string => {
  return str
    .trim()
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9]/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_+|_+$/g, '')
}

const normalizeLocation = (country?: string, state?: string, city?: string) => {
  return {
    country: normalizeLocationString(country),
    state: normalizeLocationString(state),
    city: normalizeLocationString(city),
  }
}

const isValidCoordinates = (lat?: number, lng?: number): boolean => {
  if (typeof lat !== 'number' || typeof lng !== 'number') return false
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180
}

export class TrackingService {
  private databaseRealtime: firebase.database.Database

  constructor() {
    const firebaseInstance = new FirebaseService()
    this.databaseRealtime = firebaseInstance.getDatabase()
  }

  private async updateUserCount(
    country: string,
    state: string,
    city: string,
    lat: number,
    lng: number
  ) {
    const normalized = normalizeLocation(country, state, city)
    // Update location data in the new format
    const locationsRef = this.databaseRealtime.ref('tracking/locations')
    const countryRef = locationsRef.child(normalized.country)
    const stateRef = countryRef.child(normalized.state)
    const cityRef = stateRef.child(normalized.city)
    await cityRef.transaction((currentValue) => {
      const now = Date.now()
      if (currentValue === null) {
        return {
          latitude: lat,
          longitude: lng,
          active_users: 1,
          last_updated: now,
        }
      }
      return {
        ...currentValue,
        latitude: isValidCoordinates(lat, lng) ? lat : currentValue.latitude,
        longitude: isValidCoordinates(lat, lng) ? lng : currentValue.longitude,
        active_users: (currentValue.active_users || 0) + 1,
        last_updated: now,
      }
    })
  }

  private async removeUserCount(country: string, state: string, city: string) {
    const normalized = normalizeLocation(country, state, city)
    const locationsRef = this.databaseRealtime.ref('tracking/locations')
    const countryRef = locationsRef.child(normalized.country)
    const stateRef = countryRef.child(normalized.state)
    const cityRef = stateRef.child(normalized.city)
    await cityRef.transaction((currentValue) => {
      if (currentValue === null) {
        return null
      }
      const now = Date.now()
      return {
        ...currentValue,
        active_users: Math.max((currentValue.active_users || 0) - 1, 0),
        last_updated: now,
      }
    })
  }

  private async updateTotalUsersUniqueByLocation(lat?: number | string, lng?: number | string) {
    lat = typeof lat === 'string' ? parseFloat(lat) : lat
    lng = typeof lng === 'string' ? parseFloat(lng) : lng
    if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
      return
    }
    const key = `${lat}_${lng}`.replace(/\./g, '_')
    const uniqueLocRef = this.databaseRealtime.ref(`tracking/unique_locations/${key}`)
    const snapshot = await uniqueLocRef.once('value')
    if (snapshot.exists()) {
      return
    }
    await uniqueLocRef.set(true)
    const refPath = `tracking/global_metrics/total_users`
    const totalUsersRef = this.databaseRealtime.ref(refPath)
    let afterValue
    try {
      await totalUsersRef.transaction((currentValue) => {
        afterValue = (currentValue || 0) + 1
        return afterValue
      })
    } catch (err) {}
  }

  private async cleanupInactiveUsers(timeoutMs: number = 10 * 60 * 1000) {
    const now = Date.now()
    const locationsRef = this.databaseRealtime.ref('tracking/locations')
    const snapshot = await locationsRef.once('value')
    const updates: any = {}

    snapshot.forEach((countrySnap) => {
      countrySnap.forEach((stateSnap) => {
        stateSnap.forEach((citySnap) => {
          const cityData = citySnap.val()
          if (cityData && cityData.last_updated && cityData.active_users > 0) {
            if (now - cityData.last_updated > timeoutMs) {
              updates[`${countrySnap.key}/${stateSnap.key}/${citySnap.key}/active_users`] = 0
            }
          }
        })
      })
    })

    if (Object.keys(updates).length > 0) {
      await locationsRef.update(updates)
    }
  }

  private async updateActiveUsersByLocation(
    lat?: number | string,
    lng?: number | string,
    isActive: boolean = true
  ) {
    lat = typeof lat === 'string' ? parseFloat(lat) : lat
    lng = typeof lng === 'string' ? parseFloat(lng) : lng
    if (typeof lat !== 'number' || typeof lng !== 'number' || isNaN(lat) || isNaN(lng)) {
      return
    }
    const key = `${lat}_${lng}`.replace(/\./g, '_')
    const activeLocRef = this.databaseRealtime.ref(`tracking/active_locations/${key}`)
    const globalRefPath = `tracking/global_metrics/active_users`
    const globalUsersRef = this.databaseRealtime.ref(globalRefPath)
    let afterValue
    if (isActive) {
      const snapshot = await activeLocRef.once('value')
      if (snapshot.exists()) {
        return
      }
      await activeLocRef.set(true)
      try {
        await globalUsersRef.transaction((currentValue) => {
          afterValue = (currentValue || 0) + 1
          return afterValue
        })
      } catch (err) {}
    } else {
      const snapshot = await activeLocRef.once('value')
      if (!snapshot.exists()) {
        return
      }
      await activeLocRef.remove()
      try {
        await globalUsersRef.transaction((currentValue) => {
          afterValue = Math.max((currentValue || 0) - 1, 0)
          return afterValue
        })
      } catch (err) {}
    }
  }

  async updateFirebaseTracking(
    country: string,
    state: string,
    city: string,
    lat: number | string,
    lng: number | string,
    action: TRACKING_ACTION
  ) {
    lat = typeof lat === 'string' ? parseFloat(lat) : lat
    lng = typeof lng === 'string' ? parseFloat(lng) : lng
    const updatePromises = []
    switch (action) {
      case TRACKING_ACTION.OPEN_APP:
        updatePromises.push(this.updateTotalUsersUniqueByLocation(lat, lng))
        updatePromises.push(this.updateActiveUsersByLocation(lat, lng, true))
        updatePromises.push(this.updateUserCount(country, state, city, lat, lng))
        break
      case TRACKING_ACTION.CLOSE_APP:
        updatePromises.push(this.updateActiveUsersByLocation(lat, lng, false))
        updatePromises.push(this.removeUserCount(country, state, city))
        break
      default:
        console.log(`[WARNING] Unknown action: ${action}`)
        break
    }
    await Promise.all(updatePromises)
    await this.cleanupInactiveUsers()
  }

  async create(payload: {
    userId: string
    resourceId: string
    action: TRACKING_ACTION
    resource?: string
    country?: string
    region?: string
    city?: string
    parentResourceId?: string | null
    details?: any
  }) {
    const resource =
      payload.action === TRACKING_ACTION.SHARE
        ? payload.resource || this.getResourceByAction(payload.action)
        : payload.resource || this.getResourceByAction(payload.action)

    // Dispatch both jobs
    await Promise.all([
      queue.dispatch(CreateTrackingJob, payload, {
        queueName: 'tracking',
      }),
      queue.dispatch(UpdateTrackingSnapshotJob, {
        resourceId: payload.resourceId,
        resource,
        action: payload.action,
        userId: payload.userId,
      } as any),
      {
        queueName: 'tracking',
      },
    ])
  }

  async getView(payload: TrackingServiceGetViewData) {
    return this.getInteractions({
      resourceId: payload.resourceId,
      actions: [payload.action],
    })
  }

  public actionToColumnMap: {
    [TRACKING_ACTION.VIEW_PRODUCT]: { count: string; users: string }
    [TRACKING_ACTION.VIEW_POST]: { count: string; users: string }
    [TRACKING_ACTION.VIEW_STORE]: { count: string; users: string }
    [TRACKING_ACTION.CLICK_CALL_ON_POST]: { count: string; users: string }
    [TRACKING_ACTION.CLICK_CALL_ON_STORE]: { count: string; users: string }
    [TRACKING_ACTION.ADD_WISHLIST]: { count: string; users: string }
    [TRACKING_ACTION.ADD_TO_CART]: { count: string; users: string }
    [TRACKING_ACTION.SHARE]: { count: string; users: string }
    [TRACKING_ACTION.GET_STREAM_VIEW]: { count: string; users: string }
    [TRACKING_ACTION.SEARCH]: { count: string; users: string }
  } = {
    [TRACKING_ACTION.VIEW_PRODUCT]: { count: 'viewCount', users: 'viewUsers' },
    [TRACKING_ACTION.VIEW_POST]: { count: 'viewCount', users: 'viewUsers' },
    [TRACKING_ACTION.VIEW_STORE]: { count: 'viewCount', users: 'viewUsers' },
    [TRACKING_ACTION.CLICK_CALL_ON_POST]: {
      count: 'clickCallOnCount',
      users: 'clickCallOnUsers',
    },
    [TRACKING_ACTION.CLICK_CALL_ON_STORE]: {
      count: 'clickCallOnCount',
      users: 'clickCallOnUsers',
    },
    [TRACKING_ACTION.ADD_WISHLIST]: { count: 'likeCount', users: 'likeUsers' },
    // [TRACKING_ACTION.OPEN_APP]: { count: 'openAppCount', users: 'openAppUsers' },
    // [TRACKING_ACTION.CLOSE_APP]: { count: 'closeAppCount', users: 'closeAppUsers' },
    // [TRACKING_ACTION.RATING_PRODUCT]: { count: 'ratingProductCount', users: 'ratingProductUsers' },
    // [TRACKING_ACTION.FAVORITE_PRODUCT]: {
    //   count: 'favoriteProductCount',
    //   users: 'favoriteProductUsers',
    // },
    [TRACKING_ACTION.ADD_TO_CART]: { count: 'addToCartCount', users: 'addToCartUsers' },
    [TRACKING_ACTION.SHARE]: { count: 'shareCount', users: 'shareUsers' },
    [TRACKING_ACTION.GET_STREAM_VIEW]: { count: 'viewCount', users: 'viewUsers' },
    [TRACKING_ACTION.SEARCH]: { count: 'searchCount', users: 'searchUsers' },
  }

  async getInteractions(payload: TrackingServiceGetInteractionsData) {
    const result: Record<TRACKING_ACTION, { count: number; users: any[] }> = {} as any

    // Get the snapshot for this resource
    const snapshot = await ZnResourceInteracts.query()
      .where('resourceId', payload.resourceId)
      .whereNotExists((qb) => {
        qb.from('zn_users_like_resources')
          .whereRaw('zn_users_like_resources.resourceId = zn_resource_interacts.resourceId')
          .whereNotNull('deletedAt')
      })
      .first()

    if (!snapshot) {
      // If no snapshot exists, return zero counts for all requested actions
      for (const action of payload.actions) {
        result[action] = {
          count: 0,
          users: [],
        }
      }
      return result
    }

    // Map each action to its corresponding count and users from the snapshot
    for (const action of payload.actions) {
      // @ts-ignore
      const columns = this.actionToColumnMap[action]

      if (!columns) {
        result[action] = {
          count: 0,
          users: [],
        }
        continue
      }

      const users = (snapshot[columns.users as keyof ZnResourceInteracts] as any[]) || []

      result[action] = {
        // Temporarily comment out original count logic and use users.length instead
        count: (snapshot[columns.count as keyof ZnResourceInteracts] as number) || 0,
        // count: users.length, // Use the length of users array for accurate count
        users: users,
      }
    }

    return result
  }

  async getChildResourceInteractions(payload: { parentResourceId: string; actions: number[] }) {
    let result: any = {}

    for (const action of payload.actions) {
      const query = db.from(ZnTracking.table).where({
        action: action,
        parentResourceId: payload.parentResourceId,
      })

      const aggQuery = query.clone().count('* as count')
      const aggResult = await aggQuery

      const resourceCountQuery = db
        .from(ZnTracking.table)
        .select('resourceId', 'resource')
        .count('* as count')
        .countDistinct('userId as uniqueUsers')
        .where({
          action: action,
          parentResourceId: payload.parentResourceId,
        })
        .groupBy('resourceId', 'resource')

      const resourceCounts = await resourceCountQuery

      result[action] = {
        ...aggResult[0],
        resources: resourceCounts,
      }
    }

    return result
  }

  private readonly actionResourceMap: Record<TRACKING_ACTION, string> = {
    [TRACKING_ACTION.VIEW_PRODUCT]: ZnProduct.name,
    [TRACKING_ACTION.VIEW_POST]: ZnPost.name,
    [TRACKING_ACTION.VIEW_STORE]: ZnStore.name,
    [TRACKING_ACTION.CLICK_CALL_ON_POST]: ZnPost.name,
    [TRACKING_ACTION.CLICK_CALL_ON_STORE]: ZnStore.name,
    [TRACKING_ACTION.ADD_WISHLIST]: ZnPost.name,
    [TRACKING_ACTION.OPEN_APP]: 'AppSession',
    [TRACKING_ACTION.CLOSE_APP]: 'AppSession',
    [TRACKING_ACTION.RATING_PRODUCT]: ZnProduct.name,
    [TRACKING_ACTION.FAVORITE_PRODUCT]: ZnProduct.name,
    [TRACKING_ACTION.ADD_TO_CART]: ZnProductVariant.name,
    [TRACKING_ACTION.SHARE]: ZnPost.name,
    [TRACKING_ACTION.GET_STREAM_VIEW]: ZnStream.name,
    [TRACKING_ACTION.SEARCH]: 'ZnSearch',
  }

  getResourceByAction(action: TRACKING_ACTION, resource?: string): string {
    if (action === TRACKING_ACTION.SHARE && resource) {
      return resource
    }

    const resourceType = this.actionResourceMap[action]
    if (!resourceType) {
      throw new Error(`Unknown action: ${action}`)
    }

    return resourceType
  }

  async syncInteractions(payload: TrackingServiceGetInteractionsData) {
    const awsService = new AmazonS3StorageService()
    let result: any = {}

    // Prepare actions for parallel processing
    const actionPromises = payload.actions.map(async (action) => {
      // Likes (ADD_WISHLIST)
      if (action === TRACKING_ACTION.ADD_WISHLIST) {
        // Count Likes
        const likeCount = await ZnUserLikeResource.query()
          .where({ resourceId: payload.resourceId })
          .whereNull('deletedAt')
          .count('* as count')
          .first()

        const likesCount = likeCount?.$extras?.count || 0

        // Fetch Liked Users (Limit to 5)
        const likedUsers = await db
          .from(ZnUserLikeResource.table)
          .where({ resourceId: payload.resourceId })
          .whereNull('deletedAt')
          .join(ZnUser.table, 'zn_users_like_resources.userId', 'zn_users.id')
          .select(
            'zn_users.id as userId',
            'zn_users.firstName',
            'zn_users.lastName',
            'zn_users.email',
            'zn_users.avatar',
            db.raw('MAX(zn_users_like_resources.createdAt) as lastInteraction')
          )
          .groupBy(
            'zn_users.id',
            'zn_users.firstName',
            'zn_users.lastName',
            'zn_users.email',
            'zn_users.avatar'
          )
          .limit(5)

        // Construct Avatar URLs
        likedUsers.forEach((user: any) => {
          user.avatarUrl = !user.avatar
            ? null
            : /^(http(s?)):\/\//i.test(user.avatar)
              ? user.avatar
              : awsService.getFullUrl() + user.avatar
        })

        result[action] = {
          count: likesCount,
          users: likedUsers,
        }
        return
      }

      // Stream View Count (GET_STREAM_VIEW) or Post View Count (VIEW_POST)
      let streamViewerCount = 0
      if (action === TRACKING_ACTION.GET_STREAM_VIEW || action === TRACKING_ACTION.VIEW_POST) {
        const latest = await ZnTracking.query()
          .where({
            action: action,
            resourceId: payload.resourceId,
            resource: this.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
          })
          .orderBy('createdAt', 'desc')
          .first()

        streamViewerCount = latest?.details?.total || 0

        // If it's a stream view action, return immediately
        if (action === TRACKING_ACTION.GET_STREAM_VIEW) {
          result[action] = {
            count: streamViewerCount,
            users: [],
          }
          return
        }
      }

      // Aggregate Count for Other Actions
      const [aggResult] = await ZnTracking.query()
        .where({ action: action, resourceId: payload.resourceId })
        .orWhere({ action: action, parentResourceId: payload.resourceId })
        .count('* as count')

      const count = aggResult?.$extras?.count || 0

      // Fetch Top 5 Users for the Action
      const users = await db
        .from(ZnTracking.table)
        .where({ action: action, resourceId: payload.resourceId })
        .join(ZnUser.table, 'zn_trackings.userId', 'zn_users.id')
        .select(
          'zn_users.id as userId',
          'zn_users.firstName',
          'zn_users.lastName',
          'zn_users.email',
          'zn_users.avatar',
          db.raw('MAX(zn_trackings.createdAt) as lastInteraction')
        )
        .groupBy(
          'zn_users.id',
          'zn_users.firstName',
          'zn_users.lastName',
          'zn_users.email',
          'zn_users.avatar'
        )
        .limit(5)

      // Construct Avatar URLs
      users.forEach((user: any) => {
        user.avatarUrl = !user.avatar
          ? null
          : /^(http(s?)):\/\//i.test(user.avatar)
            ? user.avatar
            : awsService.getFullUrl() + user.avatar
      })

      // Aggregate the count with stream viewer count if applicable
      result[action] = {
        count: count + streamViewerCount,
        users,
      }
    })

    // Process all actions in parallel
    await Promise.all(actionPromises)

    return result
  }

  /**
   * Track user search activity
   * @param payload Search tracking data
   */
  async trackSearch(payload: {
    userId: string
    searchTerm: string
    resource: string
    location?: {
      latitude?: string
      longitude?: string
      city?: string
      region?: string
      postCode?: string
      country?: string
      ip?: string
    }
  }) {
    // Generate a unique resourceId based on the search term
    // This helps in aggregating similar searches
    const resourceId = payload.searchTerm.toLowerCase().trim()

    // Prepare location data for the details
    const locationData = { ...payload.location }

    // Store additional metadata in the details
    const details = JSON.stringify({
      originalTerm: payload.searchTerm,
      normalizedTerm: resourceId,
      resource: payload.resource,
      timestamp: new Date().toISOString(),
      location: locationData, // Store full location data including IP in details
    })

    return this.create({
      userId: payload.userId,
      resourceId,
      action: TRACKING_ACTION.SEARCH,
      resource: payload.resource,
      country: payload.location?.country,
      region: payload.location?.region,
      city: payload.location?.city,
      details,
    })
  }

  /**
   * Get tracking query builder for analytics purposes
   */
  getTrackingQuery() {
    return ZnTracking.query()
  }

  /**
   * Get tracking query builder for popular items
   * with count aggregation
   */
  getTrackingPopularQuery() {
    return db.from(ZnTracking.table).select('resourceId').count('* as count')
  }
}

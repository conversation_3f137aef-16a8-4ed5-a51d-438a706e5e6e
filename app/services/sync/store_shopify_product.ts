import ZnChannel from '#models/zn_channel'
import ZnCollection from '#models/zn_collection'
import ZnProduct from '#models/zn_product'
import ZnProductCategory from '#models/zn_product_category'
import ZnProductImage from '#models/zn_product_image'
import ZnProductOption from '#models/zn_product_option'
import ZnProductOptionValue from '#models/zn_product_option_value'
import ZnProductTag from '#models/zn_product_tag'
import ZnProductType from '#models/zn_product_type'
import ZnProductVariant from '#models/zn_product_variant'
import ZnVendor from '#models/zn_vendor'
import { DateTime } from 'luxon'
import { formatDBDate } from '../../../services/commons.js'
import ZnVariantOptionValue from '#models/zn_variant_option_value'

export default class StoreShopifyProduct {
  async handle(products: any) {
    const dbProducts: ZnProduct[] = []

    for (const product of products) {
      const vendor = await ZnVendor.updateOrCreate(
        { companyName: product.vendor },
        { companyName: product.vendor }
      )
      let productType
      if (product.productType.trim() !== '') {
        productType = await ZnProductType.updateOrCreate(
          { name: product.productType },
          { name: product.productType }
        )
      }

      const tags: ZnProductTag[] = []
      for (let tagName of product.tags) {
        if (tagName && tagName.trim() === '') {
          continue
        }

        const tag = await ZnProductTag.updateOrCreate({ name: tagName }, { name: tagName })

        tags.push(tag)
      }

      // const tags = await ZnProductTag.updateOrCreateMany(
      //   'name',
      //   product.tags?.map((tag: string) => ({ name: tag }))
      // )

      //Product category
      let productCategory = null
      if (product.category) {
        const category = product.category
        const parent = category.parentId
          ? await ZnProductCategory.findBy({ shopifyId: category.parentId })
          : null

        productCategory = await ZnProductCategory.updateOrCreate(
          { shopifyId: category.id },
          {
            shopifyId: category.id,

            name: category.name,
            level: category.level,
            isRoot: category.isRoot,
            isLeaf: category.isLeaf,
            parentId: parent?.id, // Ensure null for root categories
            ancestorIds: JSON.stringify(category.ancestorIds),
            childrenIds: JSON.stringify(category.childrenIds),
            fullName: category.fullName,
            isArchived: category.isArchived,
          }
        )
      }

      const metafields = product.metafields?.nodes || []
      const pickupOnly = metafields?.find((metafield: any) => metafield.key === 'pickup_only')?.value === 'true'
      const isGift = metafields?.find((metafield: any) => metafield.key === 'is_gift')?.value === 'true'

      const productData = {
        shopifyProductId: product.id,
        title: product.title,
        description: product.descriptionHtml,
        // vendor: product.vendor,
        vendorId: vendor.id,
        // productType: product.productType,
        productTypeId: productType?.id,
        categoryId: productCategory?.id,
        handle: product.handle,
        tags: product.tags.join(','),
        status: product.status.toLowerCase(),
        publishedAt: this.formatDate(product.publishedAt),
        pickupOnly,
        isGift,
      }

      const newProduct = await ZnProduct.updateOrCreate({ shopifyProductId: product.id }, productData)
      await newProduct.load('collections')

      //Save Product categories
      const collectionIds = product.collections.edges.map((edge: any) => edge.node.id)
      const dbCollection = await ZnCollection.query().whereIn('shopifyCollectionId', collectionIds)
      await newProduct.related('collections').sync(dbCollection.map((i: ZnCollection) => i.id))

      //Save Product tags
      await newProduct.related('tags').sync(tags.map((tag) => tag.id))

      //Save Product channels
      const channels: ZnChannel[] = []

      for (const edge of product.resourcePublications.edges) {
        const channel = await ZnChannel.updateOrCreate(
          { shopifyChannelId: edge.node.publication.id },
          {
            shopifyChannelId: edge.node.publication.id,
            name: edge.node.publication.name,
            active: edge.node.publication.catalog.status === 'ACTIVE',
          }
        )
        channels.push(channel)
      }
      await newProduct.related('channels').sync(channels.map((c) => c.id))

      //Save product options
      await ZnProductOption.query()
        .where('productId', newProduct?.id)
        .whereNotIn('shopifyOptionId', product.options?.map((i: { id: any }) => i.id) || [])
        .update({
          deletedAt: DateTime.local().toString(),
        })

      for (const option of product.options) {
        const dbOption = await ZnProductOption.updateOrCreate(
          { shopifyOptionId: option.id },
          {
            productId: newProduct.id,
            shopifyOptionId: option.id,
            name: option.name,
            position: option.position,
          }
        )

        let position = 1
        await ZnProductOptionValue.query()
          .where('optionId', dbOption.id)
          .whereNotIn(
            'shopifyOptionValueId',
            option.optionValues?.map((i: { id: any }) => i.id) || []
          )
          .update({
            deletedAt: DateTime.local().toString(),
          })
        for (const value of option.optionValues) {
          //Option values
          await ZnProductOptionValue.updateOrCreate(
            { optionId: dbOption.id, shopifyOptionValueId: value.id },
            {
              optionId: dbOption.id,
              shopifyOptionValueId: value.id,
              value: value.name,
              position,
            }
          )

          position += 1
        }
      }
      // Save product variants
      await ZnProductVariant.query()
        .where('productId', newProduct?.id)
        .whereNotIn(
          'shopifyVariantId',
          product.variants.edges.map((i: { node: { id: any } }) => i.node.id)
        )
        .update({
          deletedAt: DateTime.local().toString(),
        })

      //Delete all images first
      await ZnProductImage.query()
        .where('productId', newProduct.id)
        .whereNotIn(
          'shopifyImageId',
          product.variants.edges
            .filter((i: { node: { image: { id: any } } }) => i.node.image)
            .map((i: { node: { image: { id: any } } }) => i.node.image.id)
        )
        .delete()

      const rawVariants = product.variants.edges
      const rawProductImages = product.images.edges
      const variantCount = rawVariants.length

      // If there is only one variant, use the product image
      if (variantCount === 1) {
        const firstVariantImage = rawVariants?.[0]?.variant?.node?.image
        if (!firstVariantImage) {
          const image = rawProductImages?.[0]?.node
          if (image) {
            // Save product images
            const productImageData = {
              shopifyImageId: image.id,
              productId: newProduct.id,
              src: image.src,
              altText: image.altText,
              width: image.width,
              height: image.height,
            }
            await ZnProductImage.updateOrCreate({ shopifyImageId: image.id }, productImageData)
          }
        }
      }

      for (const variant of rawVariants) {
        const image = variant.node.image
        let productImage = null as ZnProductImage | null
        if (image) {
          // Save product images
          const productImageData = {
            shopifyImageId: image.id,
            productId: newProduct.id,
            src: image.src,
            altText: image.altText,
            width: image.width,
            height: image.height,
          }
          productImage = (await ZnProductImage.updateOrCreate(
            { shopifyImageId: image.id },
            productImageData
          )) as ZnProductImage
        }

        const revertShopifyWeightUnit: any = {
          'GRAMS': 'g',
          "KILOGRAMS": 'kg',
          "POUNDS": 'lb',
          "OUNCES": 'oz',
        }

        let weightUnit
        if (variant.node.inventoryItem?.measurement?.weight?.unit) {
          weightUnit = revertShopifyWeightUnit[variant.node.inventoryItem?.measurement?.weight?.unit]
        }

        const variantData = {
          shopifyVariantId: variant.node.id,
          productId: newProduct.id,
          title: variant.node.title,
          legacyResourceId: variant.node.legacyResourceId,
          sku: variant.node.sku,
          price: parseFloat(variant.node.price),
          compareAtPrice: variant.node.compareAtPrice
            ? parseFloat(variant.node.compareAtPrice)
            : null,
          inventoryQuantity: variant.node.inventoryQuantity,
          inventoryPolicy: variant.node.inventoryPolicy,
          weight: variant.node.inventoryItem?.measurement?.weight?.value,
          weightUnit: weightUnit,
          barcode: variant.node.barcode,
          position: variant.node.position,
          imageId: productImage?.id,
          availableForSale: variant.node.availableForSale,
        }
        const dbVariant = await ZnProductVariant.updateOrCreate(
          { shopifyVariantId: variant.node.id },
          variantData
        )

        if (productImage) {
          productImage.variantId = dbVariant.id
          await productImage.save()
        }

        //Save selected options (old?)
        await ZnVariantOptionValue.query().where('variantId', dbVariant.id).update({
          deletedAt: DateTime.local().toString(),
        })

        for (const item of variant.node.selectedOptions) {
          const selectedOption = item.optionValue
          if (selectedOption) {
            const dbOption = await ZnProductOptionValue.findBy({
              shopifyOptionValueId: selectedOption.id,
            })
            if (dbOption) {
              await ZnVariantOptionValue.updateOrCreate(
                { variantId: dbVariant.id, productOptionId: dbOption.optionId },
                {
                  variantId: dbVariant.id,
                  productOptionId: dbOption.optionId,
                  value: selectedOption.name,
                }
              )
            }
          }
        }

        // Save selected options        
        const selectedOptionValueIds = variant.node.selectedOptions.map((option: any) => option.optionValue.id)
        const dbOptionValues = await ZnProductOptionValue.query()
          .whereIn('shopifyOptionValueId', selectedOptionValueIds)
        dbVariant.related('optionValues').sync(dbOptionValues.map(opVal => opVal.id))

        for (const [index, imageEdge] of product.images.edges.entries()) {
          const image = imageEdge.node
          await ZnProductImage.updateOrCreate(
            { shopifyImageId: image.id },
            {
              productId: newProduct.id,
              shopifyImageId: image.id,
              src: image.src,
              altText: image.altText,
              width: image.width,
              height: image.height,
              position: index + 1,
            }
          )
        }
      }

      dbProducts.push(newProduct)
    }

    return dbProducts
  }

  private formatDate(isoString: any): any {
    return formatDBDate(isoString)
  }
}

import OpenAI from "openai"


export default class RecommendationFitEvaluator {
  private openai = new OpenAI()

  /**
   * Loosely decide whether `productDescription` (or a post’s title/desc/price/location
   * bundled into one string) satisfies `userSentence`.
   * GP<PERSON> must reply with the single word **Yes** or **No**.
   */
  async isFit(userSentence: string, candidateDescription: string) {
    const prompt = `
    You are a generous product/post-matching evaluator.

    Rules for saying “Yes”:
    - Roughly 60 % overlap of key terms is enough,
      OR the product/post title is a direct hit.
    - If the text is a *post* that includes price:
        • Accept when its price is within ±20 % of any price the user asked for.
    - If the text is a *post* that includes location:
        • Accept when the location is the same city **or** within about 30 km (≈ 20 mi).
    Return **exactly** "Yes" or "No" – no other words.
        `.trim()

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4.1',
      temperature: 1.0,
      messages: [
        { role: 'system', content: prompt },
        {
          role: 'user',
          content: [
            `USER REQUEST:\n"${userSentence}"`,
            '',
            `CANDIDATE TEXT (title, description, price, location as available):`,
            `"${candidateDescription}"`,
          ].join('\n'),
        },
      ],
    })
    const link = [
        `USER REQUEST:\n"${userSentence}"`,
        '',
        `CANDIDATE TEXT (title, description, price, location as available):`,
        `"${candidateDescription}"`,
      ].join('\n')
    console.log('Evaluation string', link)
    console.log(response.choices[0].message.content?.trim().toLowerCase())
    return response.choices[0].message.content?.trim().toLowerCase() === 'yes'
  }


}

interface IAddItemToCart {
  variantId: string
  quantity: number
  cartId?: string
  userId?: string
}

interface IAddBundleToCart {
  bundleId: string
  quantity: number
  cartId?: string
  userId?: string
  discountId?: string
  items?: Array<{
    variantId: string
    itemId: string
  }>
  collections?: Array<{
    id: string
    items: Array<{
      id: string
      variants: Array<{
        id: string
        quantity: number
      }>
    }>
  }>
}

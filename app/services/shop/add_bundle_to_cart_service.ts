import ZnBundleProduct from '#models/zn_bundle_product'
import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnCartSection from '#models/zn_cart_section'
import ZnProductVariant from '#models/zn_product_variant'
import CheckoutService from '#services/checkout_service'
import {
  IAddBundleToCartCheckBundleAddOnExisted,
  IAddBundleToCartCreateSection,
  IAddBundleToCartCreateSectionForCollection,
  IAddBundleToCartHandler,
} from './add_bundle_to_cart_interface.js'
import { ShopCartService } from './shop_cart_service.js'

export class AddBundleToCartService {
  public async handle({
    cart,
    bundle,
    payloadItems,
    payloadCollections,
    payloadQuantity,
  }: IAddBundleToCartHandler) {
    if (bundle.itemType === 'collection') {
      return await this.handleCollectionBundle({
        cart,
        bundle,
        payloadCollections,
        payloadQuantity,
      })
    }

    return await this.handleProductBundle({
      cart,
      bundle,
      payloadItems,
      payloadQuantity,
    })
  }

  public async handleCollectionBundle({
    cart,
    bundle,
    payloadCollections: rawPayloadCollections,
    payloadQuantity,
  }: IAddBundleToCartHandler) {
    let payloadCollections = rawPayloadCollections
    if (!payloadCollections) {
      // If payloadCollections is not provided, create it from bundle collections
      // get fist variant of each item
      payloadCollections = []
      for (const collection of bundle.collections) {
        const item = collection.items?.[0]
        const variant = item?.variants?.[0]
        if (variant) {
          payloadCollections.push({
            id: collection.id,
            items: [
              {
                id: item.id,
                variants: [
                  {
                    id: variant.id,
                    quantity: collection.quantity,
                  },
                ],
              },
            ],
          })
        }
      }
    }
    // If payloadCollections is provided
    const rawVariantIds = payloadCollections
      .flatMap((item) => item.items.flatMap((i) => i.variants.map((v) => v.id)))
      .filter(Boolean)
    const rawVariants = await ZnProductVariant.query().whereIn('id', rawVariantIds)

    // Check if all variants are NOT available for sale
    if (!rawVariants.every((v) => v.availableForSale)) {
      throw new Error('Variant not available for sale')
    }

    // If the bundle does not exist in the cart, create a new cart section
    return await this.createCartSectionForCollection({
      cartId: cart.id,
      bundle,
      payloadCollections,
      payloadQuantity,
      variants: rawVariants,
    })
  }

  public async handleProductBundle({
    cart,
    bundle,
    payloadItems: rawPayloadItems,
    payloadQuantity,
  }: IAddBundleToCartHandler) {
    let payloadItems = rawPayloadItems
    if (!payloadItems) {
      // If payloadItems is not provided, create it from bundle items
      // get fist variant of each item
      payloadItems = []
      for (const item of bundle.items) {
        if (item.fastBundleId) {
          payloadItems.push({
            variantId: item.variants[0].id,
            itemId: item.fastBundleId,
          })
        }
      }
    } else {
      // If payloadItems is provided and is add_on bundle
      if (bundle.type === 'add_on') {
        for (const item of bundle.items) {
          // If item ignores discount, skip it (It is Add On Item)
          if (!item.ignoresDiscount) {
            continue
          }

          // If item does not ignore discount, add it to payloadItems (It is main variant)
          payloadItems.push({
            variantId: item.variants[0].id,
            itemId: item.fastBundleId as string,
          })
        }
      }
    }

    const rawVariantIds = payloadItems.map((item) => item.variantId)
    const rawVariants = await ZnProductVariant.query().whereIn('id', rawVariantIds)

    // Check if all variants are NOT available for sale
    if (!rawVariants.every((v) => v.availableForSale)) {
      throw new Error('Variant not available for sale')
    }

    let cartSection
    const cartSectionExist = await cart
      .related('cartSections')
      .query()
      .where('bundleId', bundle.id)
      .preload('cartItems')
      .first()

    // If the bundle already exists in the cart, update the quantity
    if (cartSectionExist) {
      let isUpdateQuantity = false

      if (bundle.type === 'add_on') {
        // If the bundle is add_on, check if the bundle is existed in the cart
        const checkBundleAddOnExisted = await this.checkBundleAddOnExisted({
          payloadItems,
          cartSection: cartSectionExist,
        })

        if (checkBundleAddOnExisted) {
          // If the bundle ADD_ON is existed in the cart, update the quantity
          isUpdateQuantity = true
        }
      } else {
        // If the bundle is NOT ADD_ON, update the quantity
        isUpdateQuantity = true
      }

      // Update quantity and return
      if (isUpdateQuantity) {
        const quantity = Number(cartSectionExist.quantity) + Number(payloadQuantity)
        const shopCartService = new ShopCartService()
        cartSection = await shopCartService.updateQuantityCartSection({
          cartSection: cartSectionExist,
          quantity,
        })
        if (cartSection) {
          // Refresh cart section with cart items
          await cartSection.load('cartItems')
          await cartSection.load('bundle')
          await cartSection.refresh()
        }

        return cartSection
      }
    }

    // If the bundle does not exist in the cart, create a new cart section
    return await this.createCartSection({
      cartId: cart.id,
      bundle,
      payloadItems,
      payloadQuantity,
    })
  }

  public async createCartSectionForCollection({
    cartId,
    bundle,
    payloadCollections,
    payloadQuantity,
    variants,
  }: IAddBundleToCartCreateSectionForCollection) {
    // If the bundle does not exist in the cart, create a new cart section
    // Create raw cart items (WITHOUT DISCOUNT)
    const rawCartItems: any[] = []
    let bundleProductDiscount = bundle.discounts[0]

    for (const collection of bundle.collections) {
      const payloadCollection = payloadCollections.find((c) => c.id === collection.id)
      if (!payloadCollection) {
        throw new Error('Collection not found')
      }
      for (const payloadItem of payloadCollection.items) {
        const item = collection.items.find((i) => i.id === payloadItem.id)
        if (!item) {
          throw new Error('Item not found')
        }
        for (const payloadVariant of payloadItem.variants) {
          const variant = variants.find((v) => v.id === payloadVariant.id)
          if (!variant) {
            throw new Error('Variant not found')
          }

          const quantity = payloadVariant.quantity
          const rawPrice = variant.price
          const price = variant.price
          const bundleDiscountId = collection.ignoresDiscount ? null : bundleProductDiscount.id

          const index = rawCartItems.findIndex(
            (i) => i.variantId === variant.id && i.bundleDiscountId === bundleDiscountId
          )
          if (index !== -1) {
            rawCartItems[index].quantity += quantity
            continue
          }

          // Add cart item
          rawCartItems.push({
            productId: variant.productId,
            variantId: variant.id,
            quantity,
            rawPrice,
            price,
            productName: item.title,
            variantName: variant.title,
            image: variant.image?.src || item.image,
            sku: variant.sku,
            ignoresDiscount: collection.ignoresDiscount,
            bundleDiscountId,
          })
        }
      }
    }

    return await this.generateCartSectionFromCartItem({
      rawCartItems,
      bundleProductDiscount,
      cartId,
      payloadQuantity,
      bundle,
    })
  }

  public async createCartSection({
    cartId,
    bundle,
    payloadItems,
    payloadQuantity,
  }: IAddBundleToCartCreateSection) {
    // If the bundle does not exist in the cart, create a new cart section
    // Create raw cart items (WITHOUT DISCOUNT)
    // Raw cart items is used to calculate sub total
    const rawCartItems = []
    let bundleProductDiscount = bundle.discounts[0]
    if (bundle.type === 'bogo') {
      const shopCartService = new ShopCartService()
      const discount = shopCartService.getDiscountForBundleBuyMoreGetMore(bundle, payloadQuantity)
      if (discount) {
        bundleProductDiscount = discount
      }
    }

    for (const item of bundle.items) {
      const payloadItem = payloadItems.find(
        (i) => i.itemId?.toString() === item.fastBundleId?.toString()
      )
      const variant = payloadItem
        ? item.variants.find((v) => payloadItem?.variantId === v.id)
        : null

      const quantity = item.quantity
      // If variant NOT found
      if (!variant) {
        // If the bundle is add_on, skip it
        if (bundle.type === 'add_on') {
          continue
        }
        // If the bundle is NOT add_on, throw error
        throw new Error('Variant not found')
      }
      const rawPrice = variant.price
      const price = rawPrice

      // Add cart item
      rawCartItems.push({
        productId: variant.productId,
        variantId: variant.id,
        quantity,
        rawPrice,
        price,
        productName: item.product.title,
        variantName: variant.title,
        image: variant.image?.src || item.product.image?.src,
        sku: variant.sku,
        ignoresDiscount: item.ignoresDiscount,
        bundleDiscountId: item.ignoresDiscount ? null : bundleProductDiscount.id,
      })
    }

    return await this.generateCartSectionFromCartItem({
      rawCartItems,
      bundleProductDiscount,
      cartId,
      payloadQuantity,
      bundle,
    })
  }

  private async checkBundleAddOnExisted({
    payloadItems,
    cartSection,
  }: IAddBundleToCartCheckBundleAddOnExisted) {
    const variantIds = payloadItems.map((item) => item.variantId)
    const cartItems = await cartSection.related('cartItems').query()
    const cartItemVariantIds = cartItems.map((item) => item.variantId)

    // Check if all variants are existed in the cart
    // and cartItemVariantIds.length === variantIds.length
    return (
      cartItemVariantIds.every((id) => variantIds.includes(id)) &&
      cartItemVariantIds.length === variantIds.length
    )
  }

  private async generateCartSectionFromCartItem({
    rawCartItems,
    bundleProductDiscount,
    cartId,
    payloadQuantity,
    bundle,
  }: {
    rawCartItems: any[]
    bundleProductDiscount: ZnBundleProductDiscount
    cartId: string
    payloadQuantity: number
    bundle: ZnBundleProduct
  }) {
    // Calculate sub total
    const subTotal = rawCartItems
      .filter((item) => !item.ignoresDiscount)
      .reduce((acc, item) => acc + item.price * item.quantity, 0)
    const rawPrice = subTotal
    const checkoutService = new CheckoutService()
    // Calculate discount
    const result = checkoutService.calculatorDiscountForVariant({
      discount: bundleProductDiscount,
      subTotal,
    })
    // Calculate price
    const price = checkoutService.calculatorDiscount(result, subTotal)
    // Create cart section
    const cartSection = await ZnCartSection.create({
      cartId: cartId,
      quantity: payloadQuantity,
      rawTotal: rawPrice * payloadQuantity,
      total: price * payloadQuantity,
      bundleId: bundle.id,
      title: bundle.title,
      rawPrice,
      price,
    })

    // Create cart items with discount
    const cartItems = []
    for (const item of rawCartItems) {
      let price = item.price
      if (!item.ignoresDiscount) {
        price = checkoutService.calculatorDiscount(result, item.rawPrice)
      }

      // @ts-ignore
      delete item.ignoresDiscount

      cartItems.push({
        ...item,
        price,
      })
    }

    cartSection.price = cartItems.reduce((acc, item) => acc + item.price * item.quantity, 0)
    cartSection.rawPrice = cartItems.reduce((acc, item) => acc + item.rawPrice * item.quantity, 0)
    cartSection.total = cartSection.price * payloadQuantity
    cartSection.rawTotal = (cartSection.rawPrice || 0) * payloadQuantity
    await cartSection.save()

    await cartSection.related('cartItems').createMany(cartItems)

    // Refresh cart section with cart items
    await cartSection.load('cartItems')
    await cartSection.load('bundle')
    await cartSection.refresh()

    return cartSection
  }
}

import ZnBundleProduct from '#models/zn_bundle_product'
import ZnCart from '#models/zn_cart'
import ZnCartSection from '#models/zn_cart_section'
import ZnProductVariant from '#models/zn_product_variant'

interface IAddBundleToCartPayloadItems {
  variantId: string
  itemId: string
}
interface IAddBundleToCartPayloadCollections {
  id: string
  items: {
    id: string
    variants: {
      id: string
      quantity: number
    }[]
  }[]
}

interface IAddBundleToCartHandler {
  cart: ZnCart
  bundle: ZnBundleProduct
  payloadItems?: IAddBundleToCartPayloadItems[]
  payloadCollections?: IAddBundleToCartPayloadCollections[]
  payloadQuantity: number
  discountId?: string
}

interface IAddBundleToCartCreateSection {
  cartId: string
  bundle: ZnBundleProduct
  payloadQuantity: number
  payloadItems: IAddBundleToCartPayloadItems[]
}
interface IAddBundleToCartCreateSectionForCollection {
  cartId: string
  bundle: ZnBundleProduct
  payloadQuantity: number
  payloadCollections: IAddBundleToCartPayloadCollections[]
  variants: ZnProductVariant[]
}

interface IAddBundleToCartCheckBundleAddOnExisted {
  payloadItems: IAddBundleToCartPayloadItems[]
  cartSection: ZnCartSection
}

import {PineconeEmbeddingService} from "#services/pinecone/pinecone_embedding_service";
import env from "#start/env";
import type {RecordMetadata} from "@pinecone-database/pinecone";
import ZnPost from "#models/zn_post";
import ZnPostTranslation from "#models/zn_post_translation";
import {TRANSLATIONS_ENUM} from "#constants/app";

export default class PostEmbeddingService extends PineconeEmbeddingService<ZnPost> {
  constructor() {
    const indexName = env.get('POST_EMBEDDING_INDEX_NAME') ?? 'dumpster-post'
    const embeddingModel = 'text-embedding-3-large'
    const dimension = 3072
    const batch = 4
    super(indexName, embeddingModel, dimension, batch)
  }

  protected getId(post: ZnPost) {
    return post.id
  }

  protected getTranslationValue(
    list : ZnPostTranslation[] | undefined,
    field : string,
    locale : TRANSLATIONS_ENUM,
  ) {
    if (!Array.isArray(list)) return undefined
    const translation = list.find(
      (translation): translation is ZnPostTranslation & {value: string} =>
        translation.locale === locale  && translation.field === field
    )
    return translation?.value
  }

  protected buildText(post: ZnPost) {
    const parts: string[] = []
    const DESCRIPTION_LIMIT = 1000

    const locale = 'en' as TRANSLATIONS_ENUM

    const title = this.getTranslationValue(post.translations, 'title', locale) ?? post.title
    if (title) parts.push(`Title: ${title}`)

    let description = this.getTranslationValue(post.translations, 'description', locale) ?? post.description
    if (description && description.length > DESCRIPTION_LIMIT) { description = description.slice(0, DESCRIPTION_LIMIT) }
    if (description) { parts.push(`Description: ${description}`) }

    if (post.tags)        parts.push(`Tags: ${post.tags}`)
    if (post.price != null) parts.push(`Price: ${post.price}`)

    if (post.youtubeUrl)  parts.push(`YouTube: ${post.youtubeUrl}`)
    if (post.type)        parts.push(`Type: ${post.type}`)
    if (post.source)      parts.push(`Source: ${post.source}`)

    if (post.contactName) parts.push(`Contact: ${post.contactName}`)
    if (post.phone)       parts.push(`Phone: ${post.phone}`)
    if (post.email)       parts.push(`Email: ${post.email}`)

    const address = [post.address, post.address2].filter(Boolean).join(' ');
    if (address)             parts.push(`Address: ${address}`)
    if (post.zipcode)     parts.push(`Zip: ${post.zipcode}`)

    if (post.categories.length > 0) {
      parts.push('Categories: ' + post.categories.map((category) => category.name).join(', '))
    }
    if (post.store?.name) parts.push(`Store: ${post.store.name}`)

    const location = [post.city?.name, post.state?.name, post.country?.name].filter(Boolean).join(' ')
    if (location) parts.push(`Location: ${location}`)
    return parts.join('\n')
  }

  protected buildMetadata(post: ZnPost): RecordMetadata {
    return {
      title       : post.title ?? '',
      tags        : post.tags ?? '',
      expired     : post.expired,
      type        : post.type ?? '',
      source      : post.source ?? '',
      categoryIds : (post.categories ?? []).map(category => category.id).join(','),
      price       : post.price ?? 0,
      createdAt   : post.createdAt?.toMillis() ?? 0,
      scheduledAt : post.scheduledAt?.toMillis() ?? 0,
      description : post.description ?? '',
    };
  }
}

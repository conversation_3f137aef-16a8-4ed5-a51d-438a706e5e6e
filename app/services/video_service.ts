import env from '#start/env'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'
import ffprobeInstaller from '@ffprobe-installer/ffprobe'
import { randomUUID } from 'crypto'
import ffmpeg from 'fluent-ffmpeg'
import { existsSync, readFileSync, unlink } from 'fs'
import { PassThrough } from 'stream'

export class VideoService {

  constructor() { }

  async getVideoMetadata(data: { url: string }) {
    return new Promise((resolve, reject) => {
      const ffprobePath = env.get('FFPROBE_PATH') || ffprobeInstaller.path

      ffmpeg.setFfprobePath(ffprobePath)

      ffmpeg(data.url)
        .ffprobe(0, (err, metadata) => {
          if (err) {
            reject(err)
          } else {
            resolve(metadata)
          }
        })

    })
  }

  async getVideoScreenShotBuffer({
    url,
    seek = 5,
  }: {
    url: string,
    seek?: number | 'random',
  }) {
    let innerSeek: any = 5

    let screenshotResolution = ''
    let width = 0
    let height = 0
    // width / height
    const croppedAspectRatio = 4 / 5
    let croppedWidth = 0
    let croppedHeight = 0
    let croppedPosition = ''

    let overlaySize = 0
    let overlayPosition = ''

    await this.getVideoMetadata({ url })
      .then((metadata: any) => {
        const videoData = metadata.streams?.find((stream: any) => stream.codec_type == 'video')
        if (!videoData) {
          throw Error('Cannot get video metadata')
        }

        console.log('Received video metadata...')

        let randomSeek = 1
        if (seek == 'random') {
          innerSeek = Math.floor(videoData.duration)
          randomSeek = Math.random()
        } else if (videoData.duration < seek) {
          innerSeek = Math.floor(videoData.duration)
        }

        innerSeek = (innerSeek * randomSeek).toFixed(1)


        width = videoData.width
        height = videoData.height

        if (width && height) {
          // scale down screenshot to at max 1000px width or height
          const aspectRatio = width / height
          if (width > 1000) {
            width = 1000
            height = Math.ceil(width / aspectRatio)
          } else if (height > 1000) {
            height = 1000
            width = Math.ceil(height * aspectRatio)
          }

          screenshotResolution = `${width}x${height}`

          // crop screenshot to fit 4:5 facebook ratio
          if (width < height) {
            croppedWidth = width
            croppedHeight = Math.ceil(croppedWidth / croppedAspectRatio)
          } else {
            croppedHeight = height
            croppedWidth = Math.ceil(croppedHeight * croppedAspectRatio)
          }
          croppedPosition = `${Math.ceil((width - croppedWidth) / 2)}:${Math.ceil((height - croppedHeight) / 2)}`

          // calculate play icon overlay size and position
          overlaySize = Math.ceil(Math.min(croppedWidth, croppedHeight) / 5)
          overlayPosition = `${Math.ceil((croppedWidth - overlaySize) / 2)}:${Math.ceil((croppedHeight - overlaySize) / 2)}`
        }
      })
      .catch((error) => {
        console.log(error);
        throw Error(error)
      })

    if (!screenshotResolution) {
      throw Error('Cannot get video resolution')
    }

    const ffmpegPath = env.get('FFMPEG_PATH') || ffmpegInstaller.path

    ffmpeg.setFfmpegPath(ffmpegPath)

    let screenshotStream = new PassThrough()

    const filepath = `public/thumbnails/${randomUUID()}.png`
    const result = await new Promise((resolve) => {
      // generate screenshot stream
      ffmpeg(url)
        .on('error', (error) => {
          console.log('screenshot error', error);
          resolve(null)
        })
        .on('start', () => {
          console.log('Start screenshot creating...');
        })
        .on('end', () => {
          console.log('End screenshot created...');
        })
        .seek(innerSeek)
        .takeFrames(1)
        .size(screenshotResolution)
        .outputOptions([
          '-vframes 1',
          '-f image2pipe',
          '-vcodec png'
        ])
        .pipe(screenshotStream)

      // overlay icon over screenshot, then i/o create the thumbnail file
      // return the thumbnail stream, then delete the file
      ffmpeg(screenshotStream)
        .on('error', (error) => {
          console.log('overlay error', error);
          resolve(null)
        })
        .on('start', () => {
          console.log('Start thumbnail creating...');
        })
        .on('end', () => {
          if (!existsSync(filepath)) { resolve(null) }
          else {
            console.log('Thumbnail', filepath, 'created');
            const file = readFileSync(filepath)
            resolve(file)
          }
        })
        .input('public/images/play-icon.png')
        .complexFilter([
          `[0:v]crop=${croppedWidth}:${croppedHeight}:${croppedPosition}[resizedscreenshot]`,
          `[1:v]scale=${overlaySize}:${overlaySize}[resizedoverlay]`,
          `[resizedscreenshot][resizedoverlay]overlay=${overlayPosition}`,
        ])
        .saveToFile(filepath)
        .run()
    })

    if (existsSync(filepath)) {
      unlink(filepath, (err) => {
        if (err) { throw err }
        else { console.log('Thumbnail', filepath, 'deleted'); }
      })
    }

    return result
  }
}

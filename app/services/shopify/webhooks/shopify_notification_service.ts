import SendNotificationRestockJob from '#jobs/send_notification_restock_job'
import ZnProductVariant from '#models/zn_product_variant'
import ZnRestockNotification from '#models/zn_restock_notification'
import ZnUser from '#models/zn_user'
import queue from '@rlanz/bull-queue/services/main'
import { NOTIFICATION_TYPE } from '../../../constants/notification.js'
import { ShopifyService } from '../shopify_service.js'
import NewOrderCreatedNotification from '#mails/shop/new_order_created_notification'
import mail from '@adonisjs/mail/services/main'
import ZnOrder from '#models/zn_order'
import { NotificationService } from '#services/notification_service'

export class ShopifyNotificationService {
  async orderNotification({
    shopifyOrderId,
    znOrderId,
    title,
    description,
    orderName,
    customer,
    status,
  }: {
    shopifyOrderId?: string
    znOrderId?: string
    title: string
    description: string
    orderName: string
    customer: any
    status?: string
  }) {
    if (customer && customer.email) {
      const user = await ZnUser.query().where('email', customer.email).first()

      if (!user) {
        const fullName = customer.first_name ?? '' + ' ' + customer.last_name ?? ''
        await mail.sendLater(new NewOrderCreatedNotification(customer.email, fullName, orderName))
      } else if (user.deviceToken) {
        let resourceId = ''
        if (znOrderId && znOrderId.trim() !== '') {
          resourceId = znOrderId
        } else if (shopifyOrderId && shopifyOrderId.trim() !== '') {
          const order = await ZnOrder.findByOrFail('shopifyId', shopifyOrderId)
          resourceId = order.id
        } else {
          throw Error('ZnOrderID or ShopifyOrderId is required')
        }

        const notificationService = new NotificationService()
        let rootResourceId = null
        let rootResourceType = null
        const orderRecord = await ZnOrder.query()
          .where('id', resourceId)
          .preload('orderDetails', (query) => {
            query.preload('variant', (variantQuery) => {
              variantQuery.preload('product')
            })
          })
          .first()
        if (orderRecord && orderRecord.orderDetails && orderRecord.orderDetails.length > 0) {
          rootResourceId = orderRecord.orderDetails[0]?.variant?.productId || null
          rootResourceType = 'product'
        }
        await notificationService.createIfNotExists({
          userId: user.id,
          resourceId,
          type: NOTIFICATION_TYPE.ORDER,
          title,
          description,
          resourceStatus: status,
          rootResourceId,
          rootResourceType,
        })
      }
    }
  }

  async restockNotification({
    inventoryLevelId,
    available,
  }: {
    inventoryLevelId: string
    available: number
  }) {
    if (available <= 0) {
      return
    }

    try {
      const shopifyService = new ShopifyService()
      const inventoryLevel = await shopifyService.getInventoryItemById(inventoryLevelId)

      // inventoryLevel NOT exist
      if (!inventoryLevel) {
        return
      }
      // When update inventory lotion NO online store
      if (!inventoryLevel?.location?.fulfillsOnlineOrders) {
        return
      }
      const shopifyVariant = inventoryLevel?.item?.variant
      // shopifyVariant NOT exist
      if (!shopifyVariant) {
        return
      }
      const shopifyVariantId = shopifyVariant.id
      const variant = await ZnProductVariant.query()
        .where({
          shopifyVariantId,
        })
        .preload('product')
        .first()
      if (variant) {
        const restockNotifications = await ZnRestockNotification.query()
          .where({
            variantId: variant.id,
          })
          .preload('user')

        const productName = shopifyVariant.displayName
        const title = 'Product restock'
        const description = `${productName} is back in stock! Add it to your cart and check out`

        for (const restockNotification of restockNotifications) {
          const user = restockNotification.user
          if (user) {
            await queue.dispatch(
              SendNotificationRestockJob,
              {
                title,
                description,
                shopifyVariantId,
                user,
                variantId: variant.id,
              },
              {
                queueName: 'webhook',
              }
            )
          }
        }
      }
    } catch (e) {
      console.error(e.message)
    }
  }

  async fulfilNotification(order: ZnOrder, fulfillmentStatus: string, topic: string) {
    let title = 'Order is ready'
    let description = `Your order ${order.name} is getting ready! The shipping label has been printed and your package will soon be on its way.`
    switch (fulfillmentStatus) {
      case 'in_transit':
        title = 'Order is on its way'
        description = `Your order ${order.name} is currently en route to you.`
        break
      case 'out_for_delivery':
        title = 'Order is out for delivery'
        description = `Almost there! Your order ${order.name} is out for delivery and will reach you soon. Get ready to receive it!.`
        break
      case 'delivered':
        title = 'Order is delivered'
        description = `Your order ${order.name} has been delivered! We hope you enjoy your purchase. If you have any questions, feel free to reach out.`
        break
    }

    await this.orderNotification({
      znOrderId: order.id,
      customer: order?.user,
      orderName: order.name,
      title: title,
      description: description,
      status: topic,
    })
  }
}

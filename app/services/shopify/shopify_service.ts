import { shopifyConfig } from '#config/shopify'
import { EListAction } from '#constants/list_action'
import axios, { AxiosInstance } from 'axios'
import { getShopifyCustomerId } from '../../../services/commons.js'
import {
  GQL_FETCH_COLLECTION,
  GQL_FETCH_CUSTOMER,
  GQL_FETCH_ORDERS,
  GQL_FETCH_ORDERS_WITH_IDS,
  GQL_FETCH_PRODUCT_BY_ID,
  GQL_FETCH_PRODUCTS,
  GQL_FETCH_VARIANTS_WITH_IDS,
  GQL_GET_CUSTOMER_ADDRESSES,
  GQL_GET_INVENTORY_ITEM_BY_ID,
  G<PERSON>_GET_ORDER_BY_FULFILL_ID,
  GQL_GET_ORDER_BY_FULFILL_ORDER_ID,
  GQL_MUTATION_ADD_PRODUCTS_TO_COLLECTION,
  GQL_MUTATION_CALCULATOR_DRAFT_ORDER,
  G<PERSON>_MUTATION_CANCEL_ORDER,
  GQL_MUTATION_CHANGE_DEFAULT_ADDRESS,
  GQL_MUTATION_CREATE_COLLECTION,
  GQL_MUTAT<PERSON>_CREATE_DISCOUNT_CODE,
  GQL_MUTATION_CREATE_LOCATION,
  GQL_MUTATION_CREATE_PRODUCT,
  GQL_MUTATION_CREATE_PRODUCT_VARIANTS,
  GQL_MUTATION_DEACTIVE_DISCOUNT_CODE,
  GQL_MUTATION_DELETE_DRAFT_ORDER,
  GQL_MUTATION_DELETE_PRODUCT,
  GQL_MUTATION_DRAFT_ORDER,
  GQL_MUTATION_REMOVE_PRODUCTS_FROM_COLLECTION,
  GQL_MUTATION_SET_PRODUCT,
  GQL_MUTATION_UPDATE_CUSTOMER,
  GQL_MUTATION_UPDATE_DRAFT_ORDER,
  GQL_MUTATION_UPDATE_ORDER_NOTE,
  GQL_QUERY_DISCOUNT_CODE,
  GQL_QUERY_DRAFT_ORDER,
  GQL_QUERY_LOCATIONS,
} from './graphql.js'
import { IDraftOrderCreate, IDraftOrderUpdate } from './order/draft_order.js'

export class ShopifyService {
  private shopifyAdminAPI: AxiosInstance
  constructor() {
    this.shopifyAdminAPI = axios.create({
      baseURL: `https://${shopifyConfig.storeDomain}/admin/api/${shopifyConfig.apiVersion}/`,
      headers: {
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyConfig.accessToken,
        'async': true,
        'crossDomain': true,
      },
    })
  }
  async getOrderByFulfillOrderId(id: string) {
    const query = GQL_GET_ORDER_BY_FULFILL_ORDER_ID

    let gqlBody = {
      query,
      variables: { id },
    }

    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.fulfillmentOrder?.order
  }

  async getOrderByFulfillId(id: string) {
    const query = GQL_GET_ORDER_BY_FULFILL_ID

    let gqlBody = {
      query,
      variables: { id },
    }

    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.fulfillment?.order
  }

  async getInventoryItemById(id: string) {
    const query = GQL_GET_INVENTORY_ITEM_BY_ID

    let gqlBody = {
      query,
      variables: { id },
    }

    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.inventoryLevel
  }

  async fetchProductsFromShopify(after: string | null) {
    const query = GQL_FETCH_PRODUCTS
    let gqlBody = {
      query,
      variables: { first: 50, after },
    }
    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    const products = response.data.data.products.edges.map((edge: any) => edge.node)
    const pageInfo = response.data.data.products.pageInfo

    return { products, pageInfo }
  }

  async fetchCustomers(after: string | null) {
    const query = GQL_FETCH_CUSTOMER
    let gqlBody = {
      query,
      variables: { first: 100, after },
    }
    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
    const customers = response.data.data.customers.edges.map((edge: any) => edge.node)
    const pageInfo = response.data.data.customers.pageInfo

    return { customers, pageInfo }
  }

  async fetchCollections(after: string | null) {
    const query = GQL_FETCH_COLLECTION
    let gqlBody = {
      query,
      variables: { first: 100, after },
    }
    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
    const collections = response.data.data.collections.edges.map((edge: any) => edge.node)
    const pageInfo = response.data.data.collections.pageInfo

    return { collections, pageInfo }
  }

  async fetchOrders(after: string | null, query?: string) {
    const queryOrder = GQL_FETCH_ORDERS
    let gqlBody = {
      query: queryOrder,
      variables: { after, query: [query, 'created_at:>=2024-04-03'].filter(Boolean).join(' AND ') },
    }
    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    const orders = response.data.data.orders.edges.map((edge: any) => edge.node)
    const pageInfo = response.data.data.orders.pageInfo

    return { orders, pageInfo }
  }

  async fetchOrdersWithIds(ids: string[]) {
    const query = GQL_FETCH_ORDERS_WITH_IDS
    let gqlBody = {
      query,
      variables: { ids },
    }
    const bodyContent = JSON.stringify(gqlBody)

    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
    const orders = response.data.data.nodes || []

    return { orders }
  }

  async fetchVariantsWithIds(ids: string[]) {
    const query = GQL_FETCH_VARIANTS_WITH_IDS
    const gqlBody = {
      query,
      variables: { ids },
    }
    const bodyContent = JSON.stringify(gqlBody)

    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
    const variants = response.data.data.nodes || []

    return { variants }
  }

  async calculatorDraftOrder(input: IDraftOrderCreate) {
    const query = GQL_MUTATION_CALCULATOR_DRAFT_ORDER

    // acceptAutomaticDiscounts is set to true to apply automatic discounts
    const gqlBody = {
      query,
      variables: { input: { ...input, acceptAutomaticDiscounts: true } },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.draftOrderCalculate?.calculatedDraftOrder
  }

  async createDraftOrder(input: IDraftOrderCreate) {
    const query = GQL_MUTATION_DRAFT_ORDER

    // acceptAutomaticDiscounts is set to true to apply automatic discounts
    const gqlBody = {
      query,
      variables: { input: { ...input, acceptAutomaticDiscounts: true } },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.draftOrderCreate?.draftOrder
  }

  async updateDraftOrder({ draftOrderId, ...input }: IDraftOrderUpdate) {
    const query = GQL_MUTATION_UPDATE_DRAFT_ORDER

    let data = input as any
    if (input.address) {
      data = {
        billingAddress: input.address,
        shippingAddress: input.address,
      }
    }
    if (input.lineItems) {
      data = {
        lineItems: input.lineItems,
      }
    }
    if (input.customAttributes) {
      data = {
        customAttributes: input.customAttributes,
      }
    }
    // acceptAutomaticDiscounts is set to true to apply automatic discounts
    const gqlBody = {
      query,
      variables: {
        ownerId: draftOrderId,
        input: data,
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.draftOrderUpdate?.draftOrder
  }

  async getDraftOrder(id: string) {
    const query = GQL_QUERY_DRAFT_ORDER

    // acceptAutomaticDiscounts is set to true to apply automatic discounts
    const gqlBody = {
      query,
      variables: { id },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.draftOrder
  }

  async getCustomerAddress(id: string) {
    const query = GQL_GET_CUSTOMER_ADDRESSES

    const gqlBody = {
      query,
      variables: { id: getShopifyCustomerId(id) },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.customer?.addresses ?? []
  }

  async createOrUpdateCustomerAddress(payload: TUpdateShopifyCustomer) {
    const query = GQL_MUTATION_UPDATE_CUSTOMER

    const gqlBody = {
      query,
      variables: {
        input: payload,
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.customerUpdate?.customer
  }

  async setDefaultCustomerAddress({
    customerId,
    addressId,
  }: {
    customerId: string
    addressId: string
  }) {
    const query = GQL_MUTATION_CHANGE_DEFAULT_ADDRESS
    const gqlBody = {
      query,
      variables: {
        customerId: getShopifyCustomerId(customerId),
        addressId: addressId,
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    return response?.data?.data?.customerUpdateDefaultAddress?.customer
  }
  async deleteCustomerAddress({
    customerId,
    addressId,
  }: {
    customerId: string
    addressId: string
  }) {
    const response = await this.shopifyAdminAPI.delete(
      `customers/${customerId}/addresses/${addressId}.json`
    )
    console.log(response)
    return response?.data?.data?.customerUpdateDefaultAddress?.customer
  }

  async getDiscountCode(discountCodeString: string) {
    const query = GQL_QUERY_DISCOUNT_CODE

    const gqlBody = {
      query,
      variables: { code: discountCodeString },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
    return response.data.data.codeDiscountNodeByCode
  }

  async createDiscountCode(input: any) {
    const query = GQL_MUTATION_CREATE_DISCOUNT_CODE

    const gqlBody = {
      query,
      variables: { basicCodeDiscount: input },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.userErrors) {
      console.error('createDiscountCode errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data?.discountCodeBasicCreate?.codeDiscountNode?.id
  }

  async deactiveDiscountCode(shopifyId: string) {
    const query = GQL_MUTATION_DEACTIVE_DISCOUNT_CODE

    const gqlBody = {
      query,
      variables: { id: shopifyId },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.userErrors) {
      console.error('deactiveDiscountCode errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data?.discountCodeDeactivate?.codeDiscountNode?.codeDiscount
  }

  async fetchShopifyProduct(id: string | undefined) {
    const query = GQL_FETCH_PRODUCT_BY_ID
    let gqlBody = {
      query,
      variables: { id: `gid://shopify/Product/${id}` },
    }
    let bodyContent = JSON.stringify(gqlBody)

    let response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data?.errors) {
      console.error('fetchShopifyProduct errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    }

    const product = response.data?.data?.product

    return { product }
  }

  async createCollection(input: any) {
    const query = GQL_MUTATION_CREATE_COLLECTION

    const gqlBody = {
      query,
      variables: { input: input },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('createDiscountCode errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data.collectionCreate.collection.id
  }

  async updateProductsOfCollection(
    action: EListAction,
    shopifyCollectionId: string,
    shopifyProductIds: string[]
  ) {
    const query =
      action == EListAction.ADD
        ? GQL_MUTATION_ADD_PRODUCTS_TO_COLLECTION
        : GQL_MUTATION_REMOVE_PRODUCTS_FROM_COLLECTION

    const gqlBody = {
      query,
      variables: { id: shopifyCollectionId, productIds: shopifyProductIds },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error(response.data.errors)
      throw new Error(JSON.stringify(response.data.errors))
    }

    if (action == EListAction.ADD) return response?.data?.data.collectionAddProducts.collection
    else return response?.data?.data.job
  }

  async updateOrderNote(data: { orderId: string; note: string }) {
    try {
      const query = GQL_MUTATION_UPDATE_ORDER_NOTE
      let gqlBody = {
        query,
        variables: {
          input: {
            id: data.orderId,
            note: data.note,
          },
        },
      }
      let bodyContent = JSON.stringify(gqlBody)

      await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

      return { success: true }
    } catch (error) {
      console.log(error.message)
      return { error, success: false }
    }
  }

  async cancelOrder(data: { orderId: string; note: string }) {
    try {
      const query = GQL_MUTATION_CANCEL_ORDER
      let gqlBody = {
        query,
        variables: {
          orderId: data.orderId,
          notifyCustomer: true,
          refund: true,
          restock: true,
          reason: 'CUSTOMER',
        },
      }
      let bodyContent = JSON.stringify(gqlBody)

      const resp = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
      await this.updateOrderNote(data)

      return { success: true, response: resp?.data?.data?.orderCancel }
    } catch (error) {
      console.log(error.message)
      return { error, success: false }
    }
  }

  async deleteDraftOrder(id: string) {
    try {
      const query = GQL_MUTATION_DELETE_DRAFT_ORDER
      let gqlBody = {
        query,
        variables: {
          input: {
            id,
          },
        },
      }
      let bodyContent = JSON.stringify(gqlBody)

      const resp = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)
      return { success: true, response: resp?.data?.data?.draftOrderDelete }
    } catch (error) {
      console.log(error.message)
      return { error, success: false }
    }
  }

  async createProduct(input: any, media?: any) {
    const query = GQL_MUTATION_CREATE_PRODUCT

    const gqlBody = {
      query,
      variables: {
        input: input,
        media: media
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('createProduct errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    } else if ((response?.data?.data?.productCreate?.userErrors?.length || 0) > 0) {
      console.error('createProduct userErrors:', response?.data?.data?.productCreate?.userErrors)
      throw new Error(JSON.stringify(response?.data?.data?.productCreate?.userErrors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data.productCreate.product
  }

  async createProductVariants(productId: string, variants: any[]) {
    const query = GQL_MUTATION_CREATE_PRODUCT_VARIANTS
    const gqlBody = {
      query,
      variables: {
        productId: productId,
        variants: variants,
        strategy: 'REMOVE_STANDALONE_VARIANT'
      },
    }

    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('updateProductVariants errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    } else if ((response?.data?.data?.productVariantsBulkCreate?.userErrors?.length || 0) > 0) {
      console.error('updateProductVariants userErrors:', response?.data?.data?.productVariantsBulkCreate?.userErrors)
      throw new Error(JSON.stringify(response?.data?.data?.productVariantsBulkCreate?.userErrors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data.productVariantsBulkCreate
  }

  async updateProduct(id: string, input: any) {
    const query = GQL_MUTATION_SET_PRODUCT

    const gqlBody = {
      query,
      variables: {
        identifier: { id },
        input: input,
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('updateProduct errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    } else if ((response?.data?.data?.productSet?.userErrors?.length || 0) > 0) {
      console.error('updateProduct userErrors:', response?.data?.data?.productSet?.userErrors)
      throw new Error(JSON.stringify(response?.data?.data?.productSet?.userErrors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data.productSet.product
  }

  async deleteProduct(id: string) {
    const query = GQL_MUTATION_DELETE_PRODUCT

    const gqlBody = {
      query,
      variables: {
        input: { id },
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('deleteProduct errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    } else if ((response?.data?.data?.productDelete?.userErrors?.length || 0) > 0) {
      console.error('deleteProduct userErrors:', response?.data?.data?.productDelete?.userErrors)
      throw new Error(JSON.stringify(response?.data?.data?.productDelete?.userErrors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data.productDelete
  }

  async getLocations() {
    const query = GQL_QUERY_LOCATIONS

    const gqlBody = {
      query,
      variables: {
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('getLocations errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    }

    return response?.data?.data?.locations
  }

  async createLocation(input: any) {
    const query = GQL_MUTATION_CREATE_LOCATION

    const gqlBody = {
      query,
      variables: {
        input: input
      },
    }
    const bodyContent = JSON.stringify(gqlBody)
    const response = await this.shopifyAdminAPI.post('/graphql.json', bodyContent)

    if (response.data.errors) {
      console.error('createLocation errors:', response.data.errors)
      throw new Error(JSON.stringify(response.data.errors)) // Throw a new error containing the gql errors.
    } else if ((response?.data?.data?.locationAdd?.userErrors?.length || 0) > 0) {
      console.error('createLocation userErrors:', response?.data?.data?.locationAdd?.userErrors)
      throw new Error(JSON.stringify(response?.data?.data?.locationAdd?.userErrors)) // Throw a new error containing the gql errors.
    }

    console.log(response);
    

    return response?.data?.data?.locationAdd
  }
}

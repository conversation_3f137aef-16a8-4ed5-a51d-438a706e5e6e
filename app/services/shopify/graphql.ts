export const GQL_GET_ORDER_BY_FULFILL_ORDER_ID = `
query GetOrderIdFromFulfillmentOrderId($id: ID!) {
  fulfillmentOrder(id: $id) {
    order {
      id
      name
      email
    }
  }
}`

export const GQL_GET_ORDER_BY_FULFILL_ID = `
query GetOrderIdFromFulfillmentId($id: ID!) {
  fulfillment(id: $id) {
    order {
      id
      name
      email
    }
  }
}`

export const GQL_GET_INVENTORY_ITEM_BY_ID = `
query getInventoryLevel($id: ID!) {
    inventoryLevel(id: $id) {
      id
      quantities(names: ["available"]) {
        name
        quantity
      }
      item {
        id
        variant {
          id
          availableForSale
          displayName
          sellableOnlineQuantity
        }
        sku
      }
      location {
        id
        name
        fulfillsOnlineOrders
      }
    }
  }
`

const _PRODUCT_VARIANT_NODE = `
  id
  title
  sku
  legacyResourceId
  price
  compareAtPrice
  inventoryQuantity
  availableForSale
  inventoryPolicy
  barcode
  position
  image {
    id
    src
    altText
    width
    height
  }
  selectedOptions {
    name
    value
    optionValue {
      id
      name
    }
  }
  inventoryItem {
    id
    sku
    requiresShipping
    measurement {
      id
      weight {
        value
        unit
      }
    }
    inventoryLevels(first: 10) {
      nodes {
        id
        quantities(names:["on_hand","available","committed","damaged","safety_stock","quality_control","reserved","incoming"]) {
          id
          name
          quantity
        }
        location {
          id
          name
          address {
            address1
          }
        }
      }
    }
  }
`

const _PRODUCT_EDGES = `
  id
  title
  descriptionHtml
  vendor
  productType
  handle
  tags
  status
  onlineStoreUrl
  createdAt
  updatedAt
  publishedAt
  metafields(namespace: "ext", first: 10) {
    nodes {
      namespace
      key
      value
    }
  }
  options (first: 100){
    id
    name
    values
    position
    optionValues {
      id
      name
    }
  }
  images(first: 250) {
    edges {
      node {
        id
        src
        altText
        width
        height
      }
    }
  }
  variants(first: 250) {
    edges {
      node {
        ${_PRODUCT_VARIANT_NODE}
      }
    }
  }
  collections(first: 100) {
    edges {
      node {
        id
        handle
        title
      }
    }
  }
  category {
    id
    name
    level
    isRoot
    isLeaf
    parentId
    ancestorIds
    childrenIds
    fullName
    isArchived
  }
  resourcePublications(first: 100) {
    edges {
      node {
        publication {
          id
          name
          catalog {
            status
          }
        }
      }
    }
`
export const GQL_FETCH_PRODUCTS = `
query GetAllProducts($first: Int!, $after: String) {
  products(first: $first, after: $after) {
    edges {
      node {
        ${_PRODUCT_EDGES}
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
`

export const GQL_FETCH_PRODUCT_BY_ID = `
query GetProductById($id: ID!) {
  product(id: $id) {
      ${_PRODUCT_EDGES}
    }
  }
}
`
export const GQL_FETCH_CUSTOMER = `
  query ($first: Int!, $after: String) {
    customers(first: $first, after: $after) {
      edges {
        node {
          id
          legacyResourceId
          email
          phone
          firstName
          lastName
          createdAt
          updatedAt
          addresses {
            id
            firstName
            lastName
            company
            name
            address1
            address2
            city
            province
            country
            zip
            phone
            provinceCode
            countryCodeV2
            latitude
            longitude
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`
export const GQL_FETCH_COLLECTION = `
query ($first: Int!, $after: String) {
    collections(first: $first, after: $after) {
      edges {
        node {
          id
          title
          handle
          description
          image {
            src
            altText
          }
          products(first: 250, sortKey: MANUAL) {
            edges {
              node {
                id
                title
                handle
              }
            }
          }
        }
        cursor
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`
export const GQL_FETCH_ORDERS = `
query GetOrders($after: String,$query: String) {
  orders(first: 250, after: $after, query: $query) {
    edges {
      cursor
      node {
        id
        name
        email
        displayFinancialStatus
        displayFulfillmentStatus
        createdAt
        updatedAt
        cancelledAt
        closedAt
        fulfillments(first: 100) {
          id
          status
          displayStatus
          deliveredAt
          displayStatus
          estimatedDeliveryAt
          createdAt
          trackingInfo {
            company
            number
            url
          }
        }
        totalPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        subtotalPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalDiscountsSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalTaxSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        totalShippingPriceSet {
          shopMoney {
            amount
            currencyCode
          }
        }
        customer {
          id
          legacyResourceId
          firstName
          lastName
          email
        }
        billingAddress {
          firstName
          lastName
          address1
          address2
          city
          province
          country
          zip
          phone
          provinceCode
          countryCode
        }
        shippingAddress {
          firstName
          lastName
          company
          address1
          address2
          city
          province
          country
          zip
          phone
          provinceCode
          countryCode
        }
        discountApplications(first: 250) {
          nodes {
            targetType
            targetSelection
            value {
              ... on PricingPercentageValue {
                percentage
              }
              ... on MoneyV2 {
                amount
              }
            }
          }
        }
        lineItems(first: 250) {
          edges {
            node {
              id
              title
              quantity
              variant {
                id
                title
                sku
              }
              originalUnitPriceSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              originalTotalSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
              totalDiscountSet {
                shopMoney {
                  amount
                  currencyCode
                }
              }
            }
          }
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
`

export const GQL_FETCH_ORDERS_WITH_IDS = `
query GetOrdersWithIds($ids: [ID!]!) {
  nodes(ids: $ids) {
    ...on Order {
      id
      name
      email
      displayFinancialStatus
      displayFulfillmentStatus
      createdAt
      updatedAt
      cancelledAt
      closedAt
      totalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalDiscountsSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalTaxSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      totalShippingPriceSet {
        shopMoney {
          amount
          currencyCode
        }
      }
      customer {
        id
        legacyResourceId
        firstName
        lastName
        email
      }
      billingAddress {
        firstName
        lastName
        address1
        address2
        city
        province
        country
        zip
        phone
        provinceCode
        countryCode
      }
      shippingAddress {
        firstName
        lastName
        company
        address1
        address2
        city
        province
        country
        zip
        phone
        provinceCode
        countryCode
      }
      discountApplications(first: 250) {
        nodes {
          targetType
          value {
            ... on PricingPercentageValue {
              percentage
            }
            ... on MoneyV2 {
              amount
            }
          }
        }
      }
      lineItems(first: 250) {
        edges {
          node {
            id
            title
            quantity
            variant {
              id
              title
              sku
            }
            originalUnitPriceSet {
              shopMoney {
                amount
                currencyCode
              }
            }
          }
        }
      }
    }
  }
}
`
const GQL_DRAFT_ORDER = `
{
  id
  invoiceUrl
  createdAt
  name
  note2
  email
  customer {
    id
    legacyResourceId
    firstName
    lastName
    email
  }
  appliedDiscount {
    value
    valueType
  }
  platformDiscounts {
    code
    shortSummary
    totalAmount {
      amount
    }
  }
  discountCodes
  totalShippingPrice
  totalPrice
  totalWeight
  discountCodes
  shippingLine {
    id
    title
  }
  totalPrice
  totalShippingPrice
  totalTax
  totalWeight
  subtotalPriceSet {
    shopMoney {
      amount
    }
  }
  totalDiscountsSet {
    shopMoney {
      amount
    }
  }
  totalLineItemsPriceSet {
    shopMoney {
      amount
    }
  }
  totalPriceSet {
    shopMoney {
      amount
    }
  }
  totalTaxSet {
    shopMoney {
      amount
    }
  }
  totalShippingPriceSet {
    shopMoney {
      amount
    }
  }
  billingAddress {
    address1
    address2
    company
    city
    country
    name
    zip
    provinceCode
    province
    company
    countryCode
    phone
  }
  shippingAddress {
    address1
    address2
    company
    city
    country
    name
    zip
    provinceCode
    province
    company
    countryCode
  }
  lineItems(first: 10) {
    nodes {
      id
      uuid
      title
      variant {
        id
        image {
          src
        }
      }
      appliedDiscount {
        title
        value
        valueType
        amountSet {
          shopMoney {
            amount
          }
        }
      }
      name
      sku
      image {
        src
      }
      product {
        id
      }
      variantTitle
      quantity
      originalTotalSet {
        shopMoney {
          amount
        }
      }
      totalDiscountSet {
        shopMoney {
          amount
        }
      }
      discountedUnitPriceSet {
        shopMoney {
          amount
        }
      }
      customAttributes {
        key
        value
      }
    }
  }
}
`

export const GQL_MUTATION_CALCULATOR_DRAFT_ORDER = `
mutation CalculateDraftOrder($input: DraftOrderInput!) {
  draftOrderCalculate(input: $input) {
    calculatedDraftOrder {
      totalPriceSet {
        shopMoney {
          amount
        }
      }
      subtotalPriceSet {
        shopMoney {
          amount
        }
      }
      availableShippingRates {
        handle
        price {
          amount
        }
        title
      }
    }
  }
}
`
export const GQL_MUTATION_DRAFT_ORDER = `
mutation draftOrderCreate($input: DraftOrderInput!) {
  draftOrderCreate(input: $input) {
    draftOrder ${GQL_DRAFT_ORDER}
  }
}
`
export const GQL_QUERY_DRAFT_ORDER = `
query draftOrder($id: ID!) {
  draftOrder(id: $id) ${GQL_DRAFT_ORDER}
}
`
export const GQL_MUTATION_UPDATE_DRAFT_ORDER = `
mutation draftOrderUpdate($input: DraftOrderInput!, $ownerId: ID!) {
  draftOrderUpdate(input: $input, id: $ownerId) {
    draftOrder ${GQL_DRAFT_ORDER}
  }
}
`

export const GQL_FETCH_VARIANTS_WITH_IDS = `
query variants($ids: [ID!]!) {
  nodes(ids: $ids) {
    ... on ProductVariant {
      id
      title
      sku
      price
      compareAtPrice
      availableForSale
      inventoryQuantity
      barcode
      image {
        id
        src
      }
      selectedOptions {
        name
        value
        optionValue {
          id
          name
        }
      }
      product {
        id
        title
        vendor
        productType
        handle
        tags
        status
        onlineStoreUrl
      }
    }
  }
}
`

const CART_QUERY = `
  {
    id
    createdAt
    updatedAt
    checkoutUrl
    note
    lines(first: 10) {
      edges {
        node {
          id
          quantity
          merchandise {
            ... on ProductVariant {
              id
            }
          }
        }
      }
    }
    buyerIdentity {
      email
      deliveryAddressPreferences {
        ... on MailingAddress {
          id
          name
          address1
          address2
          city
          country
          province
          zip
        }
      }
    }
    cost {
      totalAmount {
        amount
        currencyCode
      }
      subtotalAmount {
        amount
        currencyCode
      }
      totalTaxAmount {
        amount
        currencyCode
      }
      totalDutyAmount {
        amount
        currencyCode
      }
    }
    discountCodes {
      code
      applicable
    }
    attributes {
      key
      value
    }
    discountAllocations {
      discountApplication {
        value {
          ... on PricingPercentageValue {
            percentage
          }
          ... on MoneyV2 {
            amount
            currencyCode
          }
        }
      }
      discountedAmount {
        amount
      }
    }
  }
`

export const GQL_MUTATION_CREATE_CART = `
mutation cartCreate($input: CartInput) {
  cartCreate(input: $input) {
    cart ${CART_QUERY}
  }
}`

export const GQL_QUERY_CART = `
query ($id: ID!) {
  cart(id: $id) ${CART_QUERY}
}`

export const GQL_MUTATION_UPDATE_CART_NOTE = `
mutation cartNoteUpdate($cartId: ID!, $note: String!) {
  cartNoteUpdate(cartId: $cartId, note: $note) {
    cart ${CART_QUERY}
  }
}`

export const GQL_MUTATION_UPDATE_CART_DISCOUNT_CODES = `
mutation cartDiscountCodesUpdate($cartId: ID!, $discountCodes: [String!]) {
  cartDiscountCodesUpdate(cartId: $cartId, discountCodes: $discountCodes) {
    cart ${CART_QUERY}
  }
}`

export const GQL_MUTATION_UPDATE_CART_ADDRESS = `
mutation cartBuyerIdentityUpdate($buyerIdentity: CartBuyerIdentityInput!, $cartId: ID!) {
  cartBuyerIdentityUpdate(buyerIdentity: $buyerIdentity, cartId: $cartId) {
    cart ${CART_QUERY}
  }
}`

export const GQL_GET_CUSTOMER_ADDRESSES = `
query getCustomerAddresses($id: ID!) {
  customer(id: $id) {
    addresses {
      id
      firstName
      lastName
      phone
      company
      address1
      address2
      city
      province
      country
      zip
      provinceCode
    }
  }
}
`

export const GQL_QUERY_MENU = `
query getMenu($handle: String!) {
  menu(handle: $handle) {
    id
    title
    items {
      id
      type
      title
      url
      resourceId
      items {
        resourceId
        id
        title
        type
      }
    }
  }
}
`

export const GQL_QUERY_DISCOUNT_CODE = `
query codeDiscountNodeByCode($code: String!) {
  codeDiscountNodeByCode(code: $code) {
    id
    codeDiscount {
      __typename
      ... on DiscountCodeBasic {
        title
        summary
        status
        codes(first: 1) {
            nodes {
                code
            }
        }
      }
    }
  }
}
`

export const GQL_MUTATION_CREATE_DISCOUNT_CODE = `
mutation discountCodeBasicCreate($basicCodeDiscount: DiscountCodeBasicInput!) {
  discountCodeBasicCreate(basicCodeDiscount: $basicCodeDiscount) {
    codeDiscountNode {
      id
      codeDiscount {
        ... on DiscountCodeBasic {
          title
          startsAt
          endsAt
          customerGets {
            value {
              ... on DiscountPercentage {
                percentage
              }
            }
          },
        }
      }
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_DEACTIVE_DISCOUNT_CODE = `
mutation discountCodeDeactivate($id: ID!) {
  discountCodeDeactivate(id: $id) {
    codeDiscountNode {
      codeDiscount {
        ... on DiscountCodeBasic {
          title
          status
          startsAt
          endsAt
        }
      }
    }
    userErrors {
      field
      code
      message
    }
  }
}
`

export const GQL_MUTATION_CREATE_COLLECTION = `
mutation collectionCreate($input: CollectionInput!) {
  collectionCreate(input: $input) {
    collection {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_ADD_PRODUCTS_TO_COLLECTION = `
mutation collectionAddProducts($id: ID!, $productIds: [ID!]!) {
  collectionAddProducts(id: $id, productIds: $productIds) {
    collection {
      id
      title
      productsCount {
        count
      }
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_REMOVE_PRODUCTS_FROM_COLLECTION = `
mutation collectionRemoveProducts($id: ID!, $productIds: [ID!]!) {
  collectionRemoveProducts(id: $id, productIds: $productIds) {
    job {
      done
      id
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_UPDATE_ADDRESS = `
  mutation customerAddressUpdate($address: MailingAddressInput!, $addressId: ID!, $customerId: ID!, $setAsDefault: Boolean) {
    customerAddressUpdate(address: $address, addressId: $addressId, customerId: $customerId, setAsDefault: $setAsDefault) {
      address {
        id
        address1
        address2
        city
        company
        country
        phone
        province
        zip
        firstName
        lastName
      }
      userErrors {
        field
        message
      }
    }
  }
`

export const GQL_MUTATION_DELETE_ADDRESS = `
  mutation customerAddressDelete($addressId: ID!, $customerId: ID!) {
  customerAddressDelete(addressId: $addressId, customerId: $customerId) {
    deletedAddressId
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_UPDATE_CUSTOMER = `
  mutation customerUpdate($input: CustomerInput!) {
    customerUpdate(input: $input) {
      customer {
        id
        addresses {
          id
          firstName
          lastName
          name
          phone
          company
          address1
          address2
          city
          province
          country
          zip
          provinceCode
          countryCode
          latitude
          longitude
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`
export const GQL_MUTATION_CHANGE_DEFAULT_ADDRESS = `
mutation CustomerAddressDefault($addressId: ID!, $customerId: ID!) {
  customerUpdateDefaultAddress(addressId: $addressId, customerId: $customerId) {
    customer {
      id
      defaultAddress {
        id
        address1
        address2
        city
        company
        country
        phone
        province
        zip
        firstName
        lastName
        formatted
      }
      addresses {
        id
        address1
        address2
        city
        country
        firstName
        province
        zip
        company
        phone
      }
    }
    userErrors {
      field
      message
    }
  }
}
`
export const GQL_MUTATION_CANCEL_ORDER = `
  mutation OrderCancel($orderId: ID!, $notifyCustomer: Boolean, $refund: Boolean!, $restock: Boolean!, $reason: OrderCancelReason!) {
    orderCancel(
      orderId: $orderId,
      notifyCustomer: $notifyCustomer,
      refund: $refund,
      restock: $restock,
      reason: $reason
    ) {
      job {
        id
        done
      }
      orderCancelUserErrors {
        field
        message
        code
      }
    }
  }
`
export const GQL_MUTATION_DELETE_DRAFT_ORDER = `
mutation draftOrderDelete($input: DraftOrderDeleteInput!) {
  draftOrderDelete(input: $input) {
    deletedId
  }
}
`
export const GQL_MUTATION_UPDATE_ORDER_NOTE = `
  mutation updateOrderNote($input: OrderInput!) {
  orderUpdate(input: $input) {
    order {
      id
      note
    }
    userErrors {
      message
      field
    }
  }
}
`

export const GQL_MUTATION_CREATE_PRODUCT = `
mutation productCreate($input: ProductInput!, $media: [CreateMediaInput!]) {
  productCreate(input: $input, media: $media) {
    product {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_CREATE_PRODUCT_VARIANTS = `
mutation productVariantsBulkCreate($productId: ID!, $variants: [ProductVariantsBulkInput!]!, $strategy:ProductVariantsBulkCreateStrategy) {
  productVariantsBulkCreate(productId: $productId, variants: $variants, strategy: $strategy) {
    product{
        ${_PRODUCT_EDGES}
      }
    }
    productVariants {
      ${_PRODUCT_VARIANT_NODE}
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_UPDATE_PRODUCT = `
mutation productUpdate($input: ProductInput!, $media: [CreateMediaInput!]) {
  productUpdate(input: $input, media: $media) {
    product {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_SET_PRODUCT = `
mutation productSet($identifier: ProductSetIdentifiers, $input: ProductSetInput!) {
  productSet(identifier: $identifier, input: $input) {
    product {
        ${_PRODUCT_EDGES}
      }
    }
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_MUTATION_DELETE_PRODUCT = `
mutation productDelete($input: ProductDeleteInput!) {
  productDelete(input: $input) {
    deletedProductId
    
    userErrors {
      field
      message
    }
  }
}
`

export const GQL_QUERY_LOCATIONS = `
query locationsGet($after:String) {
  locations(first: 100, after: $after,) {
    edges {
      node {
        id
        name
        address {
          formatted
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
`

export const GQL_MUTATION_CREATE_LOCATION = `
mutation locationAdd($input: LocationAddInput!) {
  locationAdd(input: $input) {
    location {
      id
      name
      address {
        formatted
      }
    }
    userErrors {
      field
      message
    }
  }
}
`